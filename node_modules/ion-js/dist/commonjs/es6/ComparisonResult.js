"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ComparisonResult = exports.ComparisonResultType = void 0;
var ComparisonResultType;
(function (ComparisonResultType) {
    ComparisonResultType["EQUAL"] = "EQUAL";
    ComparisonResultType["NOT_EQUAL"] = "NOT_EQUAL";
    ComparisonResultType["ERROR"] = "ERROR";
})(ComparisonResultType = exports.ComparisonResultType || (exports.ComparisonResultType = {}));
class ComparisonResult {
    constructor(result = ComparisonResultType.EQUAL, message = "", actualIndex = 0, expectedIndex = 0) {
        this.result = result;
        this.message = message;
        this.actualIndex = actualIndex;
        this.expectedIndex = expectedIndex;
    }
}
exports.ComparisonResult = ComparisonResult;
//# sourceMappingURL=ComparisonResult.js.map