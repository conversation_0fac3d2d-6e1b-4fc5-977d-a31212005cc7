"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Import = void 0;
class Import {
    constructor(parent, symbolTable, length) {
        this._parent = parent;
        this._symbolTable = symbolTable;
        this._offset = this.parent ? this.parent.offset + this.parent.length : 1;
        this._length = length || this.symbolTable.numberOfSymbols;
    }
    get parent() {
        return this._parent;
    }
    get offset() {
        return this._offset;
    }
    get length() {
        return this._length;
    }
    get symbolTable() {
        return this._symbolTable;
    }
    getSymbolText(symbolId) {
        if (this.parent === undefined) {
            throw new Error("Illegal parent state.");
        }
        if (this.parent !== null) {
            const parentSymbol = this.parent.getSymbolText(symbolId);
            if (parentSymbol) {
                return parentSymbol;
            }
        }
        const index = symbolId - this.offset;
        if (index >= 0 && index < this.length) {
            return this.symbolTable.getSymbolText(index);
        }
        return undefined;
    }
    getSymbolId(symbolText) {
        let symbolId;
        if (this.parent !== null) {
            symbolId = this.parent.getSymbolId(symbolText);
            if (symbolId) {
                return symbolId;
            }
        }
        symbolId = this.symbolTable.getSymbolId(symbolText);
        if (symbolId !== null && symbolId !== undefined && symbolId < this.length) {
            return symbolId + this.offset;
        }
        return undefined;
    }
}
exports.Import = Import;
//# sourceMappingURL=IonImport.js.map