"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Writeable = void 0;
class Writeable {
    constructor(bufferSize) {
        this.bufferSize = bufferSize ? bufferSize : 4096;
        this.buffers = [new Uint8Array(this.bufferSize)];
        this.index = 0;
        this.clean = false;
    }
    get currentBuffer() {
        return this.buffers[this.buffers.length - 1];
    }
    get totalSize() {
        let size = 0;
        for (let i = 0; i < this.buffers.length - 1; i++) {
            size += this.buffers[i].length;
        }
        return size + this.index;
    }
    writeByte(byte) {
        this.clean = false;
        this.currentBuffer[this.index] = byte;
        this.index++;
        if (this.index === this.bufferSize) {
            this.buffers.push(new Uint8Array(this.bufferSize));
            this.index = 0;
        }
    }
    writeBytes(buf, offset, length) {
        if (offset === undefined) {
            offset = 0;
        }
        const writeLength = length !== undefined
            ? Math.min(buf.length - offset, length)
            : buf.length - offset;
        if (writeLength < this.currentBuffer.length - this.index - 1) {
            this.currentBuffer.set(buf.subarray(offset, offset + writeLength), this.index);
            this.index += writeLength;
        }
        else {
            this.buffers[this.buffers.length - 1] = this.currentBuffer.slice(0, this.index);
            this.buffers.push(buf.subarray(offset, length));
            this.buffers.push(new Uint8Array(this.bufferSize));
            this.clean = false;
            this.index = 0;
        }
    }
    getBytes() {
        if (this.clean) {
            return this.buffers[0];
        }
        const buffer = new Uint8Array(this.totalSize);
        let tempLength = 0;
        for (let i = 0; i < this.buffers.length - 1; i++) {
            buffer.set(this.buffers[i], tempLength);
            tempLength += this.buffers[i].length;
        }
        buffer.set(this.currentBuffer.subarray(0, this.index), tempLength);
        this.buffers = [buffer, new Uint8Array(this.bufferSize)];
        this.index = 0;
        this.clean = true;
        return buffer;
    }
}
exports.Writeable = Writeable;
//# sourceMappingURL=IonWriteable.js.map