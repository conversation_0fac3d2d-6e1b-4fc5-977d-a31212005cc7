/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import IntSize from "./IntSize";
import { Decimal } from "./IonDecimal";
import { Timestamp } from "./IonTimestamp";
import { IonType } from "./IonType";
export declare type ReaderScalarValue = null | boolean | number | bigint | Decimal | Timestamp | string | Uint8Array;
export interface Reader {
    position(): number;
    next(): IonType | null;
    stepIn(): void;
    stepOut(): void;
    depth(): number;
    annotations(): string[];
    fieldName(): string | null;
    isNull(): boolean;
    type(): IonType | null;
    booleanValue(): boolean | null;
    uInt8ArrayValue(): Uint8Array | null;
    decimalValue(): Decimal | null;
    numberValue(): number | null;
    bigIntValue(): bigint | null;
    intSize(): IntSize;
    stringValue(): string | null;
    timestampValue(): Timestamp | null;
    value(): ReaderScalarValue;
}
