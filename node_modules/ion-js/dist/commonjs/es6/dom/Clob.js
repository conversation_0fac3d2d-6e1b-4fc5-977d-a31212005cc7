"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Clob = void 0;
const Ion_1 = require("../Ion");
const Lob_1 = require("./Lob");
class Clob extends Lob_1.Lob(Ion_1.IonTypes.CLOB) {
    constructor(bytes, annotations = []) {
        super(bytes, annotations);
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeClob(this);
    }
    toJSON() {
        let encodedText = "";
        for (const byte of this) {
            if (byte >= 32 && byte <= 126) {
                encodedText += String.fromCharCode(byte);
                continue;
            }
            const hex = byte.toString(16);
            if (hex.length == 1) {
                encodedText += "\\u000" + hex;
            }
            else {
                encodedText += "\\u00" + hex;
            }
        }
        return encodedText;
    }
}
exports.Clob = Clob;
//# sourceMappingURL=Clob.js.map