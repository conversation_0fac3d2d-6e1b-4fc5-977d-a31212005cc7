import { Decimal, IonType, Timestamp, Writer } from "../Ion";
import { FromJsConstructor } from "./FromJsConstructor";
export interface Value {
    getType(): IonType;
    getAnnotations(): string[];
    isNull(): boolean;
    booleanValue(): boolean | null;
    numberValue(): number | null;
    bigIntValue(): bigint | null;
    decimalValue(): Decimal | null;
    stringValue(): string | null;
    dateValue(): Date | null;
    timestampValue(): Timestamp | null;
    uInt8ArrayValue(): Uint8Array | null;
    get(...pathElements: PathElement[]): Value | null;
    getAll(...pathElements: PathElement[]): Value[] | null;
    fieldNames(): string[];
    fields(): [string, Value][];
    allFields(): [string, Value[]][];
    elements(): Value[];
    as<T extends Value>(ionValueType: Constructor<T>): T;
    writeTo(writer: Writer): void;
    deleteField(name: string): boolean;
    ionEquals(other: any, options?: {
        epsilon?: number | null;
        ignoreAnnotations?: boolean;
        ignoreTimestampPrecision?: boolean;
    }): boolean;
    equals(other: any, options?: {
        epsilon?: number | null;
    }): boolean;
}
export declare type PathElement = string | number;
export declare type Constructor<T = {}> = new (...args: any[]) => T;
export declare function Value<Clazz extends Constructor>(BaseClass: Clazz, ionType: IonType, fromJsConstructor: FromJsConstructor): {
    new (...args: any[]): {
        _ionType: IonType;
        _ionAnnotations: string[];
        _unsupportedOperation<T extends Value>(functionName: string): never;
        getType(): IonType;
        _setAnnotations(annotations: string[]): void;
        getAnnotations(): string[];
        isNull(): boolean;
        booleanValue(): boolean | null;
        numberValue(): number | null;
        bigIntValue(): bigint | null;
        decimalValue(): Decimal | null;
        stringValue(): string | null;
        dateValue(): Date | null;
        timestampValue(): Timestamp | null;
        uInt8ArrayValue(): Uint8Array | null;
        fieldNames(): string[];
        fields(): [string, Value][];
        allFields(): [string, Value[]][];
        elements(): Value[];
        get(...pathElements: PathElement[]): Value | null;
        getAll(...pathElements: PathElement[]): Value[] | null;
        as<T_1 extends Value>(ionValueType: Constructor<T_1>): T_1;
        writeTo(writer: Writer): void;
        deleteField(name: string): boolean;
        _valueEquals(other: any, options?: {
            epsilon?: number | null;
            ignoreAnnotations?: boolean;
            ignoreTimestampPrecision?: boolean;
            onlyCompareIon?: boolean;
            coerceNumericType: boolean;
        }): boolean;
        equals(other: any, options?: {
            epsilon?: number | null;
        }): boolean;
        ionEquals(other: Value, options?: {
            epsilon?: number | null;
            ignoreAnnotations?: boolean;
            ignoreTimestampPrecision?: boolean;
        }): boolean;
    };
    _getIonType(): IonType;
    _fromJsValue(jsValue: any, annotations: string[]): Value;
} & Clazz;
export declare namespace Value {
    function from(value: any, annotations?: string[]): Value;
}
