"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Float = void 0;
const Ion_1 = require("../Ion");
const FromJsConstructor_1 = require("./FromJsConstructor");
const Value_1 = require("./Value");
const Decimal_1 = require("./Decimal");
const ion = __importStar(require("../Ion"));
const _fromJsConstructor = new FromJsConstructor_1.FromJsConstructorBuilder()
    .withPrimitives(FromJsConstructor_1.Primitives.Number)
    .withClassesToUnbox(Number)
    .build();
class Float extends Value_1.Value(Number, Ion_1.IonTypes.FLOAT, _fromJsConstructor) {
    constructor(value, annotations = []) {
        super(value);
        this._setAnnotations(annotations);
    }
    numberValue() {
        return +this.valueOf();
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeFloat64(this.numberValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
        coerceNumericType: false,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Float) {
            isSupportedType = true;
            valueToCompare = other.numberValue();
        }
        else if (options.coerceNumericType === true && other instanceof Decimal_1.Decimal) {
            let thisValue = new ion.Decimal(other.toString());
            return thisValue.equals(other.decimalValue());
        }
        else if (!options.onlyCompareIon) {
            if (other instanceof Number || typeof other === "number") {
                isSupportedType = true;
                valueToCompare = other.valueOf();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        let result = Object.is(this.numberValue(), valueToCompare);
        if (options.epsilon != null) {
            if (result ||
                Math.abs(this.numberValue() - valueToCompare) <= options.epsilon) {
                return true;
            }
        }
        return result;
    }
}
exports.Float = Float;
//# sourceMappingURL=Float.js.map