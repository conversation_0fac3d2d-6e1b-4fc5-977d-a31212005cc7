"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Boolean = void 0;
const Ion_1 = require("../Ion");
const FromJsConstructor_1 = require("./FromJsConstructor");
const JsValueConversion_1 = require("./JsValueConversion");
const Value_1 = require("./Value");
const _fromJsConstructor = new FromJsConstructor_1.FromJsConstructorBuilder()
    .withPrimitives(FromJsConstructor_1.Primitives.Boolean)
    .withClassesToUnbox(JsValueConversion_1._NativeJsBoolean)
    .build();
class Boolean extends Value_1.Value(JsValueConversion_1._NativeJsBoolean, Ion_1.IonTypes.BOOL, _fromJsConstructor) {
    constructor(value, annotations = []) {
        super(value);
        this._setAnnotations(annotations);
    }
    booleanValue() {
        return this.valueOf();
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeBoolean(this.booleanValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Boolean) {
            isSupportedType = true;
            valueToCompare = other.booleanValue();
        }
        else if (!options.onlyCompareIon) {
            if (typeof other === "boolean" || other instanceof JsValueConversion_1._NativeJsBoolean) {
                isSupportedType = true;
                valueToCompare = other.valueOf();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        if (this.booleanValue() !== valueToCompare) {
            return false;
        }
        return true;
    }
}
exports.Boolean = Boolean;
//# sourceMappingURL=Boolean.js.map