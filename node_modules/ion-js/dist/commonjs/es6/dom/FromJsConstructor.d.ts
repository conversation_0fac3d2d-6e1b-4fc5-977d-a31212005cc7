import { Constructor, Value } from "./Value";
export declare class FromJsConstructorBuilder {
    private _primitives;
    private _classesToUnbox;
    private _classes;
    constructor();
    withPrimitives(...primitives: string[]): FromJsConstructorBuilder;
    withClasses(...classes: Constructor[]): FromJsConstructorBuilder;
    withClassesToUnbox(...classes: Constructor[]): FromJsConstructorBuilder;
    build(): FromJsConstructor;
}
export declare class FromJsConstructor {
    private readonly _primitives;
    private readonly _classesToUnbox;
    private readonly _classes;
    constructor(_primitives: Set<string>, _classesToUnbox: Set<Constructor>, _classes: Set<Constructor>);
    construct(constructor: any, jsValue: any, annotations?: string[]): Value;
}
export declare namespace FromJsConstructor {
    const NONE: FromJsConstructor;
}
export declare const Primitives: {
    Boolean: string;
    Number: string;
    String: string;
    BigInt: string;
};
