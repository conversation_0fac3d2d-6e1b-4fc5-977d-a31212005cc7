import { Writer } from "../Ion";
import { Value } from "./Value";
import * as ion from "../Ion";
declare const Float_base: {
    new (...args: any[]): {
        _ionType: ion.IonType;
        _ionAnnotations: string[];
        _unsupportedOperation<T extends Value>(functionName: string): never;
        getType(): ion.IonType;
        _setAnnotations(annotations: string[]): void;
        getAnnotations(): string[];
        isNull(): boolean;
        booleanValue(): boolean | null;
        numberValue(): number | null;
        bigIntValue(): bigint | null;
        decimalValue(): ion.Decimal | null;
        stringValue(): string | null;
        dateValue(): Date | null;
        timestampValue(): ion.Timestamp | null;
        uInt8ArrayValue(): Uint8Array | null;
        fieldNames(): string[];
        fields(): [string, Value][];
        allFields(): [string, Value[]][];
        elements(): Value[];
        get(...pathElements: (string | number)[]): Value | null;
        getAll(...pathElements: (string | number)[]): Value[] | null;
        as<T_1 extends Value>(ionValueType: import("./Value").Constructor<T_1>): T_1;
        writeTo(writer: Writer): void;
        deleteField(name: string): boolean;
        _valueEquals(other: any, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
            onlyCompareIon?: boolean | undefined;
            coerceNumericType: boolean;
        }): boolean;
        equals(other: any, options?: {
            epsilon?: number | null | undefined;
        }): boolean;
        ionEquals(other: Value, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
        }): boolean;
    };
    _getIonType(): ion.IonType;
    _fromJsValue(jsValue: any, annotations: string[]): Value;
} & NumberConstructor;
export declare class Float extends Float_base {
    constructor(value: number, annotations?: string[]);
    numberValue(): number;
    writeTo(writer: Writer): void;
    _valueEquals(other: any, options?: {
        epsilon?: number | null;
        ignoreAnnotations?: boolean;
        ignoreTimestampPrecision?: boolean;
        onlyCompareIon?: boolean;
        coerceNumericType: boolean;
    }): boolean;
}
export {};
