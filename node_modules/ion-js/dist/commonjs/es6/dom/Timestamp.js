"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Timestamp = void 0;
const ion = __importStar(require("../Ion"));
const Ion_1 = require("../Ion");
const FromJsConstructor_1 = require("./FromJsConstructor");
const Value_1 = require("./Value");
const _fromJsConstructor = new FromJsConstructor_1.FromJsConstructorBuilder()
    .withClasses(Date, ion.Timestamp)
    .build();
class Timestamp extends Value_1.Value(Date, Ion_1.IonTypes.TIMESTAMP, _fromJsConstructor) {
    constructor(dateOrTimestamp, annotations = []) {
        let date;
        let timestamp;
        if (dateOrTimestamp instanceof Date) {
            date = dateOrTimestamp;
            timestamp = Timestamp._timestampFromDate(date);
        }
        else {
            timestamp = dateOrTimestamp;
            date = timestamp.getDate();
        }
        super(date);
        this._date = date;
        this._timestamp = timestamp;
        this._setAnnotations(annotations);
    }
    static _timestampFromDate(date) {
        const milliseconds = date.getUTCSeconds() * 1000 + date.getUTCMilliseconds();
        const fractionalSeconds = new Ion_1.Decimal(milliseconds, -3);
        return new ion.Timestamp(0, date.getUTCFullYear(), date.getUTCMonth() + 1, date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), fractionalSeconds);
    }
    timestampValue() {
        return this._timestamp;
    }
    dateValue() {
        return this._date;
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeTimestamp(this.timestampValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Timestamp) {
            isSupportedType = true;
            valueToCompare = other.timestampValue();
        }
        else if (!options.onlyCompareIon) {
            if (other instanceof ion.Timestamp) {
                isSupportedType = true;
                valueToCompare = other;
            }
            else if (other instanceof Date) {
                if (this.dateValue().getTime() === other.getTime()) {
                    return true;
                }
                else {
                    return false;
                }
            }
        }
        if (!isSupportedType) {
            return false;
        }
        if (options.ignoreTimestampPrecision) {
            return this.timestampValue().compareTo(valueToCompare) === 0;
        }
        return this.timestampValue().equals(valueToCompare);
    }
}
exports.Timestamp = Timestamp;
//# sourceMappingURL=Timestamp.js.map