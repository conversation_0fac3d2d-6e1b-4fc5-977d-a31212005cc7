"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Struct = void 0;
const Ion_1 = require("../Ion");
const FromJsConstructor_1 = require("./FromJsConstructor");
const Value_1 = require("./Value");
class Struct extends Value_1.Value(Object, Ion_1.IonTypes.STRUCT, FromJsConstructor_1.FromJsConstructor.NONE) {
    constructor(fields, annotations = []) {
        super();
        this._fields = Object.create(null);
        for (const [fieldName, fieldValue] of fields) {
            this._fields[fieldName] =
                fieldValue instanceof Value_1.Value ? [fieldValue] : fieldValue;
        }
        this._setAnnotations(annotations);
        return new Proxy(this, {
            set: function (target, name, value) {
                if (!(value instanceof Value_1.Value)) {
                    value = Value_1.Value.from(value);
                }
                target._fields[name] = [value];
                return true;
            },
            get: function (target, name) {
                if (name in target) {
                    return target[name];
                }
                let length = target._fields[name] !== undefined ? target._fields[name].length : -1;
                if (length === -1) {
                    return target._fields[name];
                }
                return target._fields[name][length - 1];
            },
            deleteProperty: function (target, name) {
                if (name in target._fields) {
                    delete target._fields[name];
                }
                return true;
            },
        });
    }
    get(...pathElements) {
        if (pathElements.length === 0) {
            throw new Error("Value#get requires at least one parameter.");
        }
        const [pathHead, ...pathTail] = pathElements;
        if (typeof pathHead !== "string") {
            throw new Error(`Cannot index into a struct with a ${typeof pathHead}.`);
        }
        const child = this._fields[pathHead];
        if (child === undefined) {
            return null;
        }
        if (pathTail.length === 0) {
            return child[child.length - 1];
        }
        return child[child.length - 1].get(...pathTail);
    }
    getAll(...pathElements) {
        if (pathElements.length === 0) {
            throw new Error("Value#get requires at least one parameter.");
        }
        const [pathHead, ...pathTail] = pathElements;
        if (typeof pathHead !== "string") {
            throw new Error(`Cannot index into a struct with a ${typeof pathHead}.`);
        }
        const child = this._fields[pathHead];
        if (child === undefined) {
            return null;
        }
        if (pathTail.length === 0) {
            return child;
        }
        let values = [];
        child.forEach((value) => values.push(...value.getAll(...pathTail)));
        return values;
    }
    fieldNames() {
        return Object.keys(this._fields);
    }
    allFields() {
        return Object.entries(this._fields);
    }
    fields() {
        let singleValueFields = Object.create(null);
        for (const [fieldName, values] of this.allFields()) {
            singleValueFields[fieldName] = values[values.length - 1];
        }
        return Object.entries(singleValueFields);
    }
    elements() {
        return Object.values(this._fields).flat();
    }
    [Symbol.iterator]() {
        return this.fields()[Symbol.iterator]();
    }
    toString() {
        return ("{" +
            [...this.allFields()]
                .map(([name, value]) => name + ": " + value)
                .join(", ") +
            "}");
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.stepIn(Ion_1.IonTypes.STRUCT);
        for (const [fieldName, values] of this.allFields()) {
            for (let value of values) {
                writer.writeFieldName(fieldName);
                value.writeTo(writer);
            }
        }
        writer.stepOut();
    }
    deleteField(name) {
        if (name in this._fields) {
            delete this._fields[name];
            return true;
        }
        return false;
    }
    toJSON() {
        let normalizedFields = Object.create(Struct.prototype);
        for (const [key, value] of this.fields()) {
            normalizedFields[key] = value;
        }
        return normalizedFields;
    }
    static _fromJsValue(jsValue, annotations) {
        if (!(jsValue instanceof Object)) {
            throw new Error(`Cannot create a dom.Struct from: ${jsValue.toString()}`);
        }
        const fields = Object.entries(jsValue).map(([key, value]) => [key, [Value_1.Value.from(value)]]);
        return new this(fields, annotations);
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Struct) {
            isSupportedType = true;
            valueToCompare = other.allFields();
        }
        else if (!options.onlyCompareIon) {
            if (typeof other === "object" || other instanceof Object) {
                isSupportedType = true;
                valueToCompare = Value_1.Value.from(other).allFields();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        if (this.allFields().length !== valueToCompare.length) {
            return false;
        }
        let matchFound = true;
        const paired = new Array(valueToCompare.length);
        for (let i = 0; matchFound && i < this.allFields().length; i++) {
            matchFound = false;
            for (let j = 0; !matchFound && j < valueToCompare.length; j++) {
                if (!paired[j]) {
                    const child = this.allFields()[i];
                    const expectedChild = valueToCompare[j];
                    matchFound =
                        child[0] === expectedChild[0] &&
                            this._ionValueEquals(child[1].sort(), expectedChild[1].sort(), options);
                    if (matchFound) {
                        paired[j] = true;
                    }
                }
            }
        }
        for (let i = 0; i < paired.length; i++) {
            if (!paired[i]) {
                matchFound = false;
                break;
            }
        }
        return matchFound;
    }
    _ionValueEquals(child, expectedChild, options) {
        if (child.length !== expectedChild.length) {
            return false;
        }
        for (let i = 0; i < child.length; i++) {
            if (options.onlyCompareIon) {
                if (!child[i].ionEquals(expectedChild[i], options)) {
                    return false;
                }
            }
            else {
                if (!child[i].equals(expectedChild[i])) {
                    return false;
                }
            }
        }
        return true;
    }
}
exports.Struct = Struct;
//# sourceMappingURL=Struct.js.map