"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.String = void 0;
const Ion_1 = require("../Ion");
const FromJsConstructor_1 = require("./FromJsConstructor");
const JsValueConversion_1 = require("./JsValueConversion");
const Value_1 = require("./Value");
const _fromJsConstructor = new FromJsConstructor_1.FromJsConstructorBuilder()
    .withPrimitives(FromJsConstructor_1.Primitives.String)
    .withClassesToUnbox(JsValueConversion_1._NativeJsString)
    .build();
class String extends Value_1.Value(JsValueConversion_1._NativeJsString, Ion_1.IonTypes.STRING, _fromJsConstructor) {
    constructor(text, annotations = []) {
        super(text);
        this._setAnnotations(annotations);
    }
    stringValue() {
        return this.toString();
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeString(this.stringValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof String) {
            isSupportedType = true;
            valueToCompare = other.stringValue();
        }
        else if (!options.onlyCompareIon) {
            if (typeof other === "string" || other instanceof JsValueConversion_1._NativeJsString) {
                isSupportedType = true;
                valueToCompare = other.valueOf();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        return this.compareValue(valueToCompare) === 0;
    }
    compareValue(expectedValue) {
        return this.stringValue().localeCompare(expectedValue);
    }
}
exports.String = String;
//# sourceMappingURL=String.js.map