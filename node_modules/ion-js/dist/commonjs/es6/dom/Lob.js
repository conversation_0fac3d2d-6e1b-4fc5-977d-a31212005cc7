"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Lob = void 0;
const Ion_1 = require("../Ion");
const FromJsConstructor_1 = require("./FromJsConstructor");
const Value_1 = require("./Value");
const _fromJsConstructor = new FromJsConstructor_1.FromJsConstructorBuilder()
    .withClasses(Uint8Array)
    .build();
function Lob(ionType) {
    return class extends Value_1.Value(Uint8Array, ionType, _fromJsConstructor) {
        constructor(data, annotations = []) {
            super(data);
            this._setAnnotations(annotations);
        }
        uInt8ArrayValue() {
            return this;
        }
        _valueEquals(other, options = {
            epsilon: null,
            ignoreAnnotations: false,
            ignoreTimestampPrecision: false,
            onlyCompareIon: true,
        }) {
            let isSupportedType = false;
            let valueToCompare = null;
            if (options.onlyCompareIon) {
                if (other.getType() === Ion_1.IonTypes.CLOB ||
                    other.getType() === Ion_1.IonTypes.BLOB) {
                    isSupportedType = true;
                    valueToCompare = other.uInt8ArrayValue();
                }
            }
            else {
                if (other instanceof Uint8Array) {
                    isSupportedType = true;
                    valueToCompare = other.valueOf();
                }
            }
            if (!isSupportedType) {
                return false;
            }
            let current = this.uInt8ArrayValue();
            let expected = valueToCompare;
            if (current.length !== expected.length) {
                return false;
            }
            for (let i = 0; i < current.length; i++) {
                if (current[i] !== expected[i]) {
                    return false;
                }
            }
            return true;
        }
    };
}
exports.Lob = Lob;
//# sourceMappingURL=Lob.js.map