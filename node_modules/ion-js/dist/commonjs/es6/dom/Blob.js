"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Blob = void 0;
const Ion_1 = require("../Ion");
const Lob_1 = require("./Lob");
class Blob extends Lob_1.Lob(Ion_1.IonTypes.BLOB) {
    constructor(data, annotations = []) {
        super(data, annotations);
    }
    toJSON() {
        return Ion_1.toBase64(this);
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeBlob(this);
    }
}
exports.Blob = Blob;
//# sourceMappingURL=Blob.js.map