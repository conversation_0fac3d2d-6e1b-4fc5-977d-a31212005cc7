"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SExpression = void 0;
const Ion_1 = require("../Ion");
const Sequence_1 = require("./Sequence");
class SExpression extends Sequence_1.Sequence(Ion_1.IonTypes.SEXP) {
    constructor(children, annotations = []) {
        super(children, annotations);
    }
    toString() {
        return "(" + this.join(" ") + ")";
    }
}
exports.SExpression = SExpression;
//# sourceMappingURL=SExpression.js.map