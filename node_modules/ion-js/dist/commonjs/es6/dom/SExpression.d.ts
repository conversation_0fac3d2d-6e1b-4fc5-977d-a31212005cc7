import { Value } from "./Value";
declare const SExpression_base: {
    new (children: Value[], annotations?: string[]): {
        [n: number]: any;
        get(...pathElements: (string | number)[]): Value | null;
        elements(): Value[];
        toString(): string;
        writeTo(writer: import("../IonWriter").Writer): void;
        _valueEquals(other: any, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
            onlyCompareIon?: boolean | undefined;
        }): boolean;
        _ionType: import("../IonType").IonType;
        _ionAnnotations: string[];
        _unsupportedOperation<T extends Value>(functionName: string): never;
        getType(): import("../IonType").IonType;
        _setAnnotations(annotations: string[]): void;
        getAnnotations(): string[];
        isNull(): boolean;
        booleanValue(): boolean | null;
        numberValue(): number | null;
        bigIntValue(): bigint | null;
        decimalValue(): import("../IonDecimal").Decimal | null;
        stringValue(): string | null;
        dateValue(): Date | null;
        timestampValue(): import("../IonTimestamp").Timestamp | null;
        uInt8ArrayValue(): Uint8Array | null;
        fieldNames(): string[];
        fields(): [string, Value][];
        allFields(): [string, Value[]][];
        getAll(...pathElements: (string | number)[]): Value[] | null;
        as<T_1 extends Value>(ionValueType: import("./Value").Constructor<T_1>): T_1;
        deleteField(name: string): boolean;
        equals(other: any, options?: {
            epsilon?: number | null | undefined;
        }): boolean;
        ionEquals(other: Value, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
        }): boolean;
        length: number;
        toLocaleString: (() => string) & (() => string);
        pop(): any;
        push(...items: any[]): number;
        concat(...items: ConcatArray<any>[]): any[];
        concat(...items: any[]): any[];
        join(separator?: string | undefined): string;
        reverse(): any[];
        shift(): any;
        slice(start?: number | undefined, end?: number | undefined): any[];
        sort(compareFn?: ((a: any, b: any) => number) | undefined): any;
        splice(start: number, deleteCount?: number | undefined): any[];
        splice(start: number, deleteCount: number, ...items: any[]): any[];
        unshift(...items: any[]): number;
        indexOf(searchElement: any, fromIndex?: number | undefined): number;
        lastIndexOf(searchElement: any, fromIndex?: number | undefined): number;
        every(callbackfn: (value: any, index: number, array: any[]) => unknown, thisArg?: any): boolean;
        some(callbackfn: (value: any, index: number, array: any[]) => unknown, thisArg?: any): boolean;
        forEach(callbackfn: (value: any, index: number, array: any[]) => void, thisArg?: any): void;
        map<U>(callbackfn: (value: any, index: number, array: any[]) => U, thisArg?: any): U[];
        filter<S extends any>(callbackfn: (value: any, index: number, array: any[]) => value is S, thisArg?: any): S[];
        filter(callbackfn: (value: any, index: number, array: any[]) => unknown, thisArg?: any): any[];
        reduce(callbackfn: (previousValue: any, currentValue: any, currentIndex: number, array: any[]) => any): any;
        reduce(callbackfn: (previousValue: any, currentValue: any, currentIndex: number, array: any[]) => any, initialValue: any): any;
        reduce<U_1>(callbackfn: (previousValue: U_1, currentValue: any, currentIndex: number, array: any[]) => U_1, initialValue: U_1): U_1;
        reduceRight(callbackfn: (previousValue: any, currentValue: any, currentIndex: number, array: any[]) => any): any;
        reduceRight(callbackfn: (previousValue: any, currentValue: any, currentIndex: number, array: any[]) => any, initialValue: any): any;
        reduceRight<U_2>(callbackfn: (previousValue: U_2, currentValue: any, currentIndex: number, array: any[]) => U_2, initialValue: U_2): U_2;
        find<S_1 extends any>(predicate: (this: void, value: any, index: number, obj: any[]) => value is S_1, thisArg?: any): S_1 | undefined;
        find(predicate: (value: any, index: number, obj: any[]) => unknown, thisArg?: any): any;
        findIndex(predicate: (value: any, index: number, obj: any[]) => unknown, thisArg?: any): number;
        fill(value: any, start?: number | undefined, end?: number | undefined): any;
        copyWithin(target: number, start: number, end?: number | undefined): any;
        [Symbol.iterator](): IterableIterator<any>;
        entries(): IterableIterator<[number, any]>;
        keys(): IterableIterator<number>;
        values(): IterableIterator<any>;
        [Symbol.unscopables](): {
            copyWithin: boolean;
            entries: boolean;
            fill: boolean;
            find: boolean;
            findIndex: boolean;
            keys: boolean;
            values: boolean;
        };
        includes(searchElement: any, fromIndex?: number | undefined): boolean;
        flatMap<U_3, This = undefined>(callback: (this: This, value: any, index: number, array: any[]) => U_3 | readonly U_3[], thisArg?: This | undefined): U_3[];
        flat<A, D extends number = 1>(this: A, depth?: D | undefined): FlatArray<A, D>[];
    };
    _fromJsValue(jsValue: any, annotations: string[]): Value;
    _getIonType(): import("../IonType").IonType;
    isArray(arg: any): arg is any[];
    from<T_2>(arrayLike: ArrayLike<T_2>): T_2[];
    from<T_3, U_4>(arrayLike: ArrayLike<T_3>, mapfn: (v: T_3, k: number) => U_4, thisArg?: any): U_4[];
    from<T_4>(iterable: Iterable<T_4> | ArrayLike<T_4>): T_4[];
    from<T_5, U_5>(iterable: Iterable<T_5> | ArrayLike<T_5>, mapfn: (v: T_5, k: number) => U_5, thisArg?: any): U_5[];
    of<T_6>(...items: T_6[]): T_6[];
    readonly [Symbol.species]: ArrayConstructor;
};
export declare class SExpression extends SExpression_base {
    constructor(children: Value[], annotations?: string[]);
    toString(): string;
}
export {};
