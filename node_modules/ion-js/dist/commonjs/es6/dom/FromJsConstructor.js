"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Primitives = exports.FromJsConstructor = exports.FromJsConstructorBuilder = void 0;
const Ion_1 = require("../Ion");
const util_1 = require("../util");
function _newSet(values) {
    if (util_1._hasValue(values)) {
        return new Set(values);
    }
    return new Set();
}
class FromJsConstructorBuilder {
    constructor() {
        this._primitives = _newSet();
        this._classesToUnbox = _newSet();
        this._classes = _newSet();
    }
    withPrimitives(...primitives) {
        this._primitives = _newSet(primitives);
        return this;
    }
    withClasses(...classes) {
        this._classes = _newSet(classes);
        return this;
    }
    withClassesToUnbox(...classes) {
        this._classesToUnbox = _newSet(classes);
        return this;
    }
    build() {
        return new FromJsConstructor(this._primitives, this._classesToUnbox, this._classes);
    }
}
exports.FromJsConstructorBuilder = FromJsConstructorBuilder;
class FromJsConstructor {
    constructor(_primitives, _classesToUnbox, _classes) {
        this._primitives = _primitives;
        this._classesToUnbox = _classesToUnbox;
        this._classes = _classes;
    }
    construct(constructor, jsValue, annotations = []) {
        if (jsValue === null) {
            return new Ion_1.dom.Null(Ion_1.IonTypes.NULL, annotations);
        }
        const jsValueType = typeof jsValue;
        if (jsValueType === "object") {
            if (this._classesToUnbox.has(jsValue.constructor)) {
                return new constructor(jsValue.valueOf(), annotations);
            }
            if (this._classes.has(jsValue.constructor)) {
                return new constructor(jsValue, annotations);
            }
            throw new Error(`Unable to construct a(n) ${constructor.name} from a ${jsValue.constructor.name}.`);
        }
        if (this._primitives.has(jsValueType)) {
            return new constructor(jsValue, annotations);
        }
        throw new Error(`Unable to construct a(n) ${constructor.name} from a ${jsValueType}.`);
    }
}
exports.FromJsConstructor = FromJsConstructor;
(function (FromJsConstructor) {
    FromJsConstructor.NONE = new FromJsConstructorBuilder().build();
})(FromJsConstructor = exports.FromJsConstructor || (exports.FromJsConstructor = {}));
exports.Primitives = {
    Boolean: "boolean",
    Number: "number",
    String: "string",
    BigInt: "bigint",
};
//# sourceMappingURL=FromJsConstructor.js.map