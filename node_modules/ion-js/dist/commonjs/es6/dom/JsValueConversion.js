"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports._ionValueFromJsValue = exports._domConstructorFor = exports._NativeJsString = exports._NativeJsBoolean = void 0;
const Ion_1 = require("../Ion");
const IonTypes_1 = require("../IonTypes");
const util_1 = require("../util");
exports._NativeJsBoolean = Boolean;
exports._NativeJsString = String;
let _domTypesByIonType = null;
function _getDomTypesByIonTypeMap() {
    if (_domTypesByIonType === null) {
        _domTypesByIonType = new Map([
            [IonTypes_1.IonTypes.NULL, Ion_1.dom.Null],
            [IonTypes_1.IonTypes.BOOL, Ion_1.dom.Boolean],
            [IonTypes_1.IonTypes.INT, Ion_1.dom.Integer],
            [IonTypes_1.IonTypes.FLOAT, Ion_1.dom.Float],
            [IonTypes_1.IonTypes.DECIMAL, Ion_1.dom.Decimal],
            [IonTypes_1.IonTypes.TIMESTAMP, Ion_1.dom.Timestamp],
            [IonTypes_1.IonTypes.STRING, Ion_1.dom.String],
            [IonTypes_1.IonTypes.BLOB, Ion_1.dom.Blob],
            [IonTypes_1.IonTypes.LIST, Ion_1.dom.List],
            [IonTypes_1.IonTypes.STRUCT, Ion_1.dom.Struct],
        ]);
    }
    return _domTypesByIonType;
}
function _domConstructorFor(ionType) {
    const domConstructor = _getDomTypesByIonTypeMap().get(ionType);
    if (!util_1._hasValue(domConstructor)) {
        throw new Error(`No dom type constructor was found for Ion type ${ionType.name}`);
    }
    return domConstructor;
}
exports._domConstructorFor = _domConstructorFor;
function _inferType(value) {
    if (value === undefined) {
        throw new Error("Cannot create an Ion value from `undefined`.");
    }
    if (value === null) {
        return IonTypes_1.IonTypes.NULL;
    }
    const valueType = typeof value;
    switch (valueType) {
        case "string":
            return IonTypes_1.IonTypes.STRING;
        case "number":
            return Number.isInteger(value) ? IonTypes_1.IonTypes.INT : IonTypes_1.IonTypes.FLOAT;
        case "boolean":
            return IonTypes_1.IonTypes.BOOL;
        case "object":
            break;
        case "bigint":
            return IonTypes_1.IonTypes.INT;
        default:
            throw new Error(`Value.from() does not support the JS primitive type ${valueType}.`);
    }
    if (value instanceof BigInt) {
        return IonTypes_1.IonTypes.INT;
    }
    if (value instanceof Number) {
        return Number.isInteger(value.valueOf()) ? IonTypes_1.IonTypes.INT : IonTypes_1.IonTypes.FLOAT;
    }
    if (value instanceof Boolean) {
        return IonTypes_1.IonTypes.BOOL;
    }
    if (value instanceof String) {
        return IonTypes_1.IonTypes.STRING;
    }
    if (value instanceof Ion_1.Decimal) {
        return IonTypes_1.IonTypes.DECIMAL;
    }
    if (value instanceof Date || value instanceof Ion_1.Timestamp) {
        return IonTypes_1.IonTypes.TIMESTAMP;
    }
    if (value instanceof Uint8Array) {
        return IonTypes_1.IonTypes.BLOB;
    }
    if (value instanceof Array) {
        return IonTypes_1.IonTypes.LIST;
    }
    return IonTypes_1.IonTypes.STRUCT;
}
function _ionValueFromJsValue(value, annotations = []) {
    const ionType = _inferType(value);
    const ionTypeConstructor = _domConstructorFor(ionType);
    return ionTypeConstructor._fromJsValue(value, annotations);
}
exports._ionValueFromJsValue = _ionValueFromJsValue;
//# sourceMappingURL=JsValueConversion.js.map