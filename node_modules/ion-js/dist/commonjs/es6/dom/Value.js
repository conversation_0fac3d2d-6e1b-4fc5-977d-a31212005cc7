"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Value = void 0;
const util_1 = require("../util");
const JsValueConversion = __importStar(require("./JsValueConversion"));
const _DOM_VALUE_SIGNET = Symbol("ion.dom.Value");
function Value(BaseClass, ionType, fromJsConstructor) {
    const newClass = class extends BaseClass {
        constructor(...args) {
            super(...args);
            this._ionType = ionType;
            this._ionAnnotations = [];
            Object.defineProperty(this, "_ionType", { enumerable: false });
            Object.defineProperty(this, "_ionAnnotations", { enumerable: false });
        }
        _unsupportedOperation(functionName) {
            throw new Error(`Value#${functionName}() is not supported by Ion type ${this.getType().name}`);
        }
        getType() {
            return this._ionType;
        }
        _setAnnotations(annotations) {
            this._ionAnnotations = annotations;
        }
        getAnnotations() {
            if (this._ionAnnotations === null) {
                return [];
            }
            return this._ionAnnotations;
        }
        isNull() {
            return false;
        }
        booleanValue() {
            this._unsupportedOperation("booleanValue");
        }
        numberValue() {
            this._unsupportedOperation("numberValue");
        }
        bigIntValue() {
            this._unsupportedOperation("bigIntValue");
        }
        decimalValue() {
            this._unsupportedOperation("decimalValue");
        }
        stringValue() {
            this._unsupportedOperation("stringValue");
        }
        dateValue() {
            this._unsupportedOperation("dateValue");
        }
        timestampValue() {
            this._unsupportedOperation("timestampValue");
        }
        uInt8ArrayValue() {
            this._unsupportedOperation("uInt8ArrayValue");
        }
        fieldNames() {
            this._unsupportedOperation("fieldNames");
        }
        fields() {
            this._unsupportedOperation("fields");
        }
        allFields() {
            this._unsupportedOperation("allFields");
        }
        elements() {
            this._unsupportedOperation("elements");
        }
        get(...pathElements) {
            this._unsupportedOperation("get");
        }
        getAll(...pathElements) {
            this._unsupportedOperation("getAll");
        }
        as(ionValueType) {
            if (this instanceof ionValueType) {
                return this;
            }
            throw new Error(`${this.constructor.name} is not an instance of ${ionValueType.name}`);
        }
        writeTo(writer) {
            this._unsupportedOperation("writeTo");
        }
        deleteField(name) {
            this._unsupportedOperation("deleteField");
        }
        _valueEquals(other, options = {
            epsilon: null,
            ignoreAnnotations: false,
            ignoreTimestampPrecision: false,
            onlyCompareIon: true,
            coerceNumericType: false,
        }) {
            this._unsupportedOperation("_valueEquals");
        }
        equals(other, options = { epsilon: null }) {
            let onlyCompareIon = false;
            if (other instanceof Value) {
                onlyCompareIon = true;
            }
            return this._valueEquals(other, {
                onlyCompareIon: onlyCompareIon,
                ignoreTimestampPrecision: true,
                ignoreAnnotations: true,
                epsilon: options.epsilon,
                coerceNumericType: true,
            });
        }
        ionEquals(other, options = {
            epsilon: null,
            ignoreAnnotations: false,
            ignoreTimestampPrecision: false,
        }) {
            if (!options.ignoreAnnotations) {
                if (!(other instanceof Value)) {
                    return false;
                }
                let actualAnnotations = this.getAnnotations();
                let expectedAnnotations = other.getAnnotations();
                if (actualAnnotations.length !== expectedAnnotations.length) {
                    return false;
                }
                for (let i = 0; i < actualAnnotations.length; i++) {
                    if (actualAnnotations[i].localeCompare(expectedAnnotations[i]) !== 0) {
                        return false;
                    }
                }
            }
            let ion_options = {
                onlyCompareIon: true,
                ignoreTimestampPrecision: options.ignoreTimestampPrecision,
                epsilon: options.epsilon,
                coerceNumericType: false,
            };
            return this._valueEquals(other, ion_options);
        }
        static _getIonType() {
            return ionType;
        }
        static _fromJsValue(jsValue, annotations) {
            return fromJsConstructor.construct(this, jsValue, annotations);
        }
    };
    Object.defineProperty(newClass, _DOM_VALUE_SIGNET, {
        writable: false,
        enumerable: false,
        value: _DOM_VALUE_SIGNET,
    });
    return newClass;
}
exports.Value = Value;
(function (Value) {
    function from(value, annotations) {
        if (value instanceof Value) {
            if (util_1._hasValue(annotations)) {
                throw new Error("Value.from() does not support overriding the annotations on a dom.Value" +
                    " passed as an argument.");
            }
            return value;
        }
        return JsValueConversion._ionValueFromJsValue(value, annotations);
    }
    Value.from = from;
})(Value = exports.Value || (exports.Value = {}));
Object.defineProperty(Value, Symbol.hasInstance, {
    get: () => (instance) => {
        return (util_1._hasValue(instance) &&
            util_1._hasValue(instance.constructor) &&
            _DOM_VALUE_SIGNET in instance.constructor &&
            instance.constructor[_DOM_VALUE_SIGNET] === _DOM_VALUE_SIGNET);
    },
});
//# sourceMappingURL=Value.js.map