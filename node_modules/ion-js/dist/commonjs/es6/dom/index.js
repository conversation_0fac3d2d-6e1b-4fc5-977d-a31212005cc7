"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.load = exports.loadAll = void 0;
const ion = __importStar(require("../Ion"));
const Ion_1 = require("../Ion");
const IonBinaryReader_1 = require("../IonBinaryReader");
const IonTextReader_1 = require("../IonTextReader");
const Blob_1 = require("./Blob");
const Clob_1 = require("./Clob");
const Decimal_1 = require("./Decimal");
const Float_1 = require("./Float");
const Integer_1 = require("./Integer");
const List_1 = require("./List");
const Null_1 = require("./Null");
const SExpression_1 = require("./SExpression");
const Struct_1 = require("./Struct");
const Symbol_1 = require("./Symbol");
const Timestamp_1 = require("./Timestamp");
function loadAll(ionData) {
    const reader = _createReader(ionData);
    const ionValues = [];
    while (reader.next()) {
        ionValues.push(_loadValue(reader));
    }
    return ionValues;
}
exports.loadAll = loadAll;
function load(ionData) {
    const reader = _createReader(ionData);
    if (reader.type() === null) {
        reader.next();
    }
    return reader.type() === null ? null : _loadValue(reader);
}
exports.load = load;
function _createReader(ionData) {
    if (ionData instanceof IonTextReader_1.TextReader || ionData instanceof IonBinaryReader_1.BinaryReader) {
        return ionData;
    }
    return Ion_1.makeReader(ionData);
}
function _loadValue(reader) {
    const ionType = reader.type();
    if (ionType === null) {
        throw new Error("loadValue() called when no further values were available to read.");
    }
    const annotations = reader.annotations();
    if (reader.isNull()) {
        return new Null_1.Null(reader.type(), annotations);
    }
    switch (ionType) {
        case Ion_1.IonTypes.NULL:
            return new Null_1.Null(Ion_1.IonTypes.NULL, annotations);
        case Ion_1.IonTypes.BOOL:
            return new ion.dom.Boolean(reader.booleanValue(), annotations);
        case Ion_1.IonTypes.INT:
            return reader.intSize() == Ion_1.IntSize.Number
                ? new Integer_1.Integer(reader.numberValue(), annotations)
                : new Integer_1.Integer(reader.bigIntValue(), annotations);
        case Ion_1.IonTypes.FLOAT:
            return new Float_1.Float(reader.numberValue(), annotations);
        case Ion_1.IonTypes.DECIMAL:
            return new Decimal_1.Decimal(reader.decimalValue(), annotations);
        case Ion_1.IonTypes.TIMESTAMP:
            return new Timestamp_1.Timestamp(reader.timestampValue(), annotations);
        case Ion_1.IonTypes.SYMBOL:
            return new Symbol_1.Symbol(reader.stringValue(), annotations);
        case Ion_1.IonTypes.STRING:
            return new ion.dom.String(reader.stringValue(), annotations);
        case Ion_1.IonTypes.CLOB:
            return new Clob_1.Clob(reader.uInt8ArrayValue(), annotations);
        case Ion_1.IonTypes.BLOB:
            return new Blob_1.Blob(reader.uInt8ArrayValue(), annotations);
        case Ion_1.IonTypes.LIST:
            return _loadList(reader);
        case Ion_1.IonTypes.SEXP:
            return _loadSExpression(reader);
        case Ion_1.IonTypes.STRUCT:
            return _loadStruct(reader);
        default:
            throw new Error(`Unrecognized IonType '${ionType}' found.`);
    }
}
function _loadStruct(reader) {
    const children = new Map();
    const annotations = reader.annotations();
    reader.stepIn();
    while (reader.next()) {
        if (children.has(reader.fieldName())) {
            children.get(reader.fieldName()).push(_loadValue(reader));
        }
        else {
            children.set(reader.fieldName(), [_loadValue(reader)]);
        }
    }
    reader.stepOut();
    return new Struct_1.Struct(children.entries(), annotations);
}
function _loadList(reader) {
    const annotations = reader.annotations();
    return new List_1.List(_loadSequence(reader), annotations);
}
function _loadSExpression(reader) {
    const annotations = reader.annotations();
    return new SExpression_1.SExpression(_loadSequence(reader), annotations);
}
function _loadSequence(reader) {
    const children = [];
    reader.stepIn();
    while (reader.next()) {
        children.push(_loadValue(reader));
    }
    reader.stepOut();
    return children;
}
var Value_1 = require("./Value");
Object.defineProperty(exports, "Value", { enumerable: true, get: function () { return Value_1.Value; } });
var Null_2 = require("./Null");
Object.defineProperty(exports, "Null", { enumerable: true, get: function () { return Null_2.Null; } });
var Boolean_1 = require("./Boolean");
Object.defineProperty(exports, "Boolean", { enumerable: true, get: function () { return Boolean_1.Boolean; } });
var Integer_2 = require("./Integer");
Object.defineProperty(exports, "Integer", { enumerable: true, get: function () { return Integer_2.Integer; } });
var Float_2 = require("./Float");
Object.defineProperty(exports, "Float", { enumerable: true, get: function () { return Float_2.Float; } });
var Decimal_2 = require("./Decimal");
Object.defineProperty(exports, "Decimal", { enumerable: true, get: function () { return Decimal_2.Decimal; } });
var Timestamp_2 = require("./Timestamp");
Object.defineProperty(exports, "Timestamp", { enumerable: true, get: function () { return Timestamp_2.Timestamp; } });
var String_1 = require("./String");
Object.defineProperty(exports, "String", { enumerable: true, get: function () { return String_1.String; } });
var Symbol_2 = require("./Symbol");
Object.defineProperty(exports, "Symbol", { enumerable: true, get: function () { return Symbol_2.Symbol; } });
var Blob_2 = require("./Blob");
Object.defineProperty(exports, "Blob", { enumerable: true, get: function () { return Blob_2.Blob; } });
var Clob_2 = require("./Clob");
Object.defineProperty(exports, "Clob", { enumerable: true, get: function () { return Clob_2.Clob; } });
var Struct_2 = require("./Struct");
Object.defineProperty(exports, "Struct", { enumerable: true, get: function () { return Struct_2.Struct; } });
var List_2 = require("./List");
Object.defineProperty(exports, "List", { enumerable: true, get: function () { return List_2.List; } });
var SExpression_2 = require("./SExpression");
Object.defineProperty(exports, "SExpression", { enumerable: true, get: function () { return SExpression_2.SExpression; } });
//# sourceMappingURL=index.js.map