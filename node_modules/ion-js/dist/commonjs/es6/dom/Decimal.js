"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Decimal = void 0;
const ion = __importStar(require("../Ion"));
const Ion_1 = require("../Ion");
const FromJsConstructor_1 = require("./FromJsConstructor");
const Value_1 = require("./Value");
const Float_1 = require("./Float");
const _fromJsConstructor = new FromJsConstructor_1.FromJsConstructorBuilder()
    .withClasses(ion.Decimal)
    .build();
class Decimal extends Value_1.Value(Number, Ion_1.IonTypes.DECIMAL, _fromJsConstructor) {
    constructor(value, annotations = []) {
        if (typeof value === "string") {
            let numberValue = Number(value);
            super(numberValue);
            this._decimalValue = new ion.Decimal(value);
            this._numberValue = numberValue;
        }
        else if (value instanceof ion.Decimal) {
            super(value.numberValue());
            this._decimalValue = value;
            this._numberValue = value.numberValue();
        }
        else if (typeof value === "number") {
            super(value);
            this._decimalValue = new ion.Decimal("" + value);
            this._numberValue = value;
        }
        else {
            throw new Error("Decimal value can only be created from number, ion.Decimal or string");
        }
        this._setAnnotations(annotations);
    }
    numberValue() {
        return this._numberValue;
    }
    decimalValue() {
        return this._decimalValue;
    }
    toString() {
        return this._decimalValue.toString();
    }
    valueOf() {
        return this._numberValue;
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeDecimal(this.decimalValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
        coerceNumericType: false,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Decimal) {
            isSupportedType = true;
            valueToCompare = other.decimalValue();
        }
        else if (options.coerceNumericType === true && other instanceof Float_1.Float) {
            isSupportedType = true;
            valueToCompare = new ion.Decimal(other.toString());
        }
        else if (!options.onlyCompareIon) {
            if (other instanceof ion.Decimal) {
                isSupportedType = true;
                valueToCompare = other;
            }
            else if (other instanceof Number || typeof other === "number") {
                isSupportedType = true;
                valueToCompare = new ion.Decimal(other.toString());
            }
        }
        if (!isSupportedType) {
            return false;
        }
        return this.decimalValue().equals(valueToCompare);
    }
}
exports.Decimal = Decimal;
//# sourceMappingURL=Decimal.js.map