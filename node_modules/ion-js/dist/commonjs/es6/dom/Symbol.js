"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Symbol = void 0;
const Ion_1 = require("../Ion");
const FromJsConstructor_1 = require("./FromJsConstructor");
const Value_1 = require("./Value");
const _fromJsConstructor = new FromJsConstructor_1.FromJsConstructorBuilder()
    .withPrimitives(FromJsConstructor_1.Primitives.String)
    .withClassesToUnbox(String)
    .build();
class Symbol extends Value_1.Value(String, Ion_1.IonTypes.SYMBOL, _fromJsConstructor) {
    constructor(symbolText, annotations = []) {
        super(symbolText);
        this._setAnnotations(annotations);
    }
    stringValue() {
        return this.toString();
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeSymbol(this.stringValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Symbol) {
            isSupportedType = true;
            valueToCompare = other.stringValue();
        }
        else if (!options.onlyCompareIon) {
            if (typeof other === "string" || other instanceof String) {
                isSupportedType = true;
                valueToCompare = other.valueOf();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        return this.compareValue(valueToCompare) === 0;
    }
    compareValue(expectedValue) {
        return this.stringValue().localeCompare(expectedValue);
    }
}
exports.Symbol = Symbol;
//# sourceMappingURL=Symbol.js.map