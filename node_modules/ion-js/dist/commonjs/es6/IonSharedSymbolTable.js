"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharedSymbolTable = void 0;
class SharedSymbolTable {
    constructor(_name, _version, _symbols) {
        this._name = _name;
        this._version = _version;
        this._symbols = _symbols;
        this._idsByText = new Map();
        this._numberOfSymbols = this._symbols.length;
        for (let m = _symbols.length - 1; m >= 0; m--) {
            this._idsByText.set(_symbols[m], m);
        }
    }
    get numberOfSymbols() {
        return this._numberOfSymbols;
    }
    get name() {
        return this._name;
    }
    get version() {
        return this._version;
    }
    getSymbolText(symbolId) {
        if (symbolId < 0) {
            throw new Error(`Index ${symbolId} is out of bounds for the SharedSymbolTable name=${this.name}, version=${this.version}`);
        }
        if (symbolId >= this.numberOfSymbols) {
            return undefined;
        }
        return this._symbols[symbolId];
    }
    getSymbolId(text) {
        return this._idsByText.get(text);
    }
}
exports.SharedSymbolTable = SharedSymbolTable;
//# sourceMappingURL=IonSharedSymbolTable.js.map