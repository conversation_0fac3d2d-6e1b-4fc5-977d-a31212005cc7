/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { Decimal } from "./IonDecimal";
export declare enum TimestampPrecision {
    YEAR = 1,
    MONTH = 2,
    DAY = 3,
    HOUR_AND_MINUTE = 4,
    SECONDS = 5
}
export declare class Timestamp {
    private static _MIN_SECONDS;
    private static _MAX_SECONDS;
    private static _MIN_MINUTE;
    private static _MAX_MINUTE;
    private static _MIN_HOUR;
    private static _MAX_HOUR;
    private static _MIN_DAY;
    private static _MAX_DAY;
    private static _MIN_MONTH;
    private static _MAX_MONTH;
    private static _MIN_YEAR;
    private static _MAX_YEAR;
    private static _MIN_OFFSET;
    private static _MAX_OFFSET;
    private readonly _localOffset;
    private readonly _year;
    private readonly _month?;
    private readonly _day?;
    private readonly _hour?;
    private readonly _minutes?;
    private _precision;
    private readonly _secondsDecimal;
    constructor(date: Date);
    constructor(localOffset: number, year: number, month?: number | null, day?: number | null, hour?: number | null, minutes?: number | null, seconds?: number | Decimal | null);
    static parse(str: string): Timestamp | null;
    static _adjustMsSinceEpochIfNeeded(year: number, msSinceEpoch: number): number;
    static _splitSecondsDecimal(secondsDecimal: Decimal): [string, string];
    static _valueOf(date: Date, localOffset: number, fractionalSeconds?: Decimal, precision?: TimestampPrecision): Timestamp;
    getLocalOffset(): number;
    getPrecision(): TimestampPrecision;
    getDate(): Date;
    getSecondsInt(): number;
    getSecondsDecimal(): Decimal;
    _getFractionalSeconds(): Decimal;
    equals(that: Timestamp): boolean;
    compareTo(that: Timestamp): number;
    toString(): string;
    toJSON(): string;
    private _checkRequiredField;
    private _checkOptionalField;
    private _checkFieldRange;
    private _isLeapYear;
    private _lpadZeros;
}
