"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isSafeInteger = exports._assertDefined = exports._hasValue = exports._sign = void 0;
function _sign(x) {
    return x < 0 || (x === 0 && 1 / x === -Infinity) ? -1 : 1;
}
exports._sign = _sign;
function _hasValue(v) {
    return v !== undefined && v !== null;
}
exports._hasValue = _hasValue;
function _assertDefined(value) {
    if (value === undefined) {
        throw new Error("Expected value to be defined");
    }
}
exports._assertDefined = _assertDefined;
function isSafeInteger(value) {
    return value >= Number.MIN_SAFE_INTEGER && value <= Number.MAX_SAFE_INTEGER;
}
exports.isSafeInteger = isSafeInteger;
//# sourceMappingURL=util.js.map