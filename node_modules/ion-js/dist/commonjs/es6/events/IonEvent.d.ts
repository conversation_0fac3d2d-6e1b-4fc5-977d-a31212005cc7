/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { ComparisonResult } from "../ComparisonResult";
import { IonType } from "../IonType";
import { Writer } from "../IonWriter";
export declare enum IonEventType {
    SCALAR = 0,
    CONTAINER_START = 1,
    CONTAINER_END = 2,
    SYMBOL_TABLE = 3,
    STREAM_END = 4
}
export interface IonEvent {
    eventType: IonEventType;
    ionType: IonType | null;
    fieldName: string | null;
    annotations: string[];
    depth: number;
    ionValue: any;
    write(writer: Writer): void;
    equals(expected: IonEvent): boolean;
    compare(expected: IonEvent): ComparisonResult;
    writeIonValue(writer: Writer): void;
}
export declare class IonEventFactory {
    makeEvent(eventType: IonEventType, ionType: IonType, fieldName: string | null, depth: number, annotations: string[], isNull: boolean, value: any): IonEvent;
}
