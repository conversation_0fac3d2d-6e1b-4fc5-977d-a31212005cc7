/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { StringSpan } from "./IonSpan";
import { SymbolToken } from "./IonSymbolToken";
import { IonType } from "./IonType";
export declare function get_ion_type(t: number): IonType | null;
export declare class ParserTextRaw {
    private _in;
    private _ops;
    private _value_type;
    private _value_null;
    private _value;
    private _start;
    private _end;
    private _esc_len;
    private _curr;
    private _curr_null;
    private _ann;
    private _msg;
    private _error_msg;
    private _fieldname;
    private _fieldnameType;
    private readonly _read_value_helper_helpers;
    constructor(source: StringSpan);
    fieldName(): string | null;
    fieldNameType(): number | null;
    source(): StringSpan;
    annotations(): SymbolToken[];
    clearFieldName(): void;
    isNull(): boolean;
    bigIntValue(): bigint | null;
    numberValue(): number | null;
    booleanValue(): boolean | null;
    get_value_as_string(t: number): string;
    get_value_as_uint8array(t: number): Uint8Array;
    next(): number | undefined;
    private _read_datagram_values;
    private _read_sexp_values;
    private _read_list_values;
    private _read_struct_values;
    private _read_list_comma;
    private _read_struct_comma;
    private _load_field_name;
    private _read_value;
    private _read_sexp_value;
    private _read_value_helper;
    private _read_value_helper_EOF;
    private _read_value_helper_paren;
    private _read_value_helper_square;
    private _read_value_helper_curly;
    private _read_value_helper_plus;
    private _read_value_helper_minus;
    private _read_value_helper_digit;
    private _read_value_helper_single;
    private _read_value_helper_double;
    private _read_value_helper_letter;
    private _read_value_helper_operator;
    private _done;
    private _done_with_error;
    private _read_number;
    private _read_hex_int;
    private _read_exponent;
    private _read_plus_inf;
    private _read_minus_inf;
    private _read_inf_helper;
    private _readTimestamp;
    private _read_symbol;
    private _read_operator_symbol;
    private _read_string1;
    private _read_string2;
    private _read_string3;
    private verifyTriple;
    private _read_string_helper;
    private _read_string_escape_sequence;
    private _test_string_as_annotation;
    private _read_clob_string2;
    private _read_clob_string3;
    private _read_blob;
    private _read_close_double_brace;
    private isHighSurrogate;
    private isLowSurrogate;
    private indexWhiteSpace;
    private indexToNewLine;
    private indexToCloseComment;
    private _skip_triple_quote_gap;
    private readClobEscapes;
    private _read_escape_sequence;
    private _get_N_hexdigits;
    private _value_push;
    private _value_pop;
    private _run;
    private _read;
    private _read_skipping_comments;
    private _read_to_newline;
    private _read_to_close_comment;
    private _unread;
    private _read_after_whitespace;
    private _peek;
    private _peek_4_digits;
    private _read_required_digits;
    private _read_optional_digits;
    private _readNDigits;
    private _readPastNDigits;
    private _read_required_hex_digits;
    private _read_N_hexdigits;
    private _parseSymbolId;
    private _error;
}
