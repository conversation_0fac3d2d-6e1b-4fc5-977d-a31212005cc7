"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dom = exports.IntSize = exports.dumpPrettyText = exports.dumpText = exports.dumpBinary = exports.makeBinaryWriter = exports.makePrettyWriter = exports.makeTextWriter = exports.makeReader = void 0;
const IntSize_1 = __importDefault(require("./IntSize"));
exports.IntSize = IntSize_1.default;
const IonBinaryReader_1 = require("./IonBinaryReader");
const IonBinaryWriter_1 = require("./IonBinaryWriter");
const IonConstants_1 = require("./IonConstants");
const IonLocalSymbolTable_1 = require("./IonLocalSymbolTable");
const IonPrettyTextWriter_1 = require("./IonPrettyTextWriter");
const IonSpan_1 = require("./IonSpan");
const IonTextReader_1 = require("./IonTextReader");
const IonTextWriter_1 = require("./IonTextWriter");
const IonUnicode_1 = require("./IonUnicode");
const IonWriteable_1 = require("./IonWriteable");
function isBinary(buffer) {
    if (buffer.length < 4) {
        return false;
    }
    for (let i = 0; i < 4; i++) {
        if (buffer[i] !== IonConstants_1.IVM.binary[i]) {
            return false;
        }
    }
    return true;
}
function makeReader(buf, catalog) {
    if (typeof buf === "string") {
        return new IonTextReader_1.TextReader(new IonSpan_1.StringSpan(buf), catalog);
    }
    const bufArray = new Uint8Array(buf);
    if (isBinary(bufArray)) {
        return new IonBinaryReader_1.BinaryReader(new IonSpan_1.BinarySpan(bufArray), catalog);
    }
    else {
        return new IonTextReader_1.TextReader(new IonSpan_1.StringSpan(IonUnicode_1.decodeUtf8(bufArray)), catalog);
    }
}
exports.makeReader = makeReader;
function makeTextWriter() {
    return new IonTextWriter_1.TextWriter(new IonWriteable_1.Writeable());
}
exports.makeTextWriter = makeTextWriter;
function makePrettyWriter(indentSize) {
    return new IonPrettyTextWriter_1.PrettyTextWriter(new IonWriteable_1.Writeable(), indentSize);
}
exports.makePrettyWriter = makePrettyWriter;
function makeBinaryWriter() {
    const localSymbolTable = IonLocalSymbolTable_1.defaultLocalSymbolTable();
    return new IonBinaryWriter_1.BinaryWriter(localSymbolTable, new IonWriteable_1.Writeable());
}
exports.makeBinaryWriter = makeBinaryWriter;
function _writeAllTo(writer, values) {
    for (const value of values) {
        dom.Value.from(value).writeTo(writer);
    }
    writer.close();
    return writer.getBytes();
}
function dumpBinary(...values) {
    return _writeAllTo(makeBinaryWriter(), values);
}
exports.dumpBinary = dumpBinary;
function dumpText(...values) {
    return IonUnicode_1.decodeUtf8(_writeAllTo(makeTextWriter(), values));
}
exports.dumpText = dumpText;
function dumpPrettyText(...values) {
    return IonUnicode_1.decodeUtf8(_writeAllTo(makePrettyWriter(), values));
}
exports.dumpPrettyText = dumpPrettyText;
var IonCatalog_1 = require("./IonCatalog");
Object.defineProperty(exports, "Catalog", { enumerable: true, get: function () { return IonCatalog_1.Catalog; } });
var IonDecimal_1 = require("./IonDecimal");
Object.defineProperty(exports, "Decimal", { enumerable: true, get: function () { return IonDecimal_1.Decimal; } });
var IonLocalSymbolTable_2 = require("./IonLocalSymbolTable");
Object.defineProperty(exports, "defaultLocalSymbolTable", { enumerable: true, get: function () { return IonLocalSymbolTable_2.defaultLocalSymbolTable; } });
var IonType_1 = require("./IonType");
Object.defineProperty(exports, "IonType", { enumerable: true, get: function () { return IonType_1.IonType; } });
var IonTypes_1 = require("./IonTypes");
Object.defineProperty(exports, "IonTypes", { enumerable: true, get: function () { return IonTypes_1.IonTypes; } });
var IonSharedSymbolTable_1 = require("./IonSharedSymbolTable");
Object.defineProperty(exports, "SharedSymbolTable", { enumerable: true, get: function () { return IonSharedSymbolTable_1.SharedSymbolTable; } });
var IonTimestamp_1 = require("./IonTimestamp");
Object.defineProperty(exports, "TimestampPrecision", { enumerable: true, get: function () { return IonTimestamp_1.TimestampPrecision; } });
Object.defineProperty(exports, "Timestamp", { enumerable: true, get: function () { return IonTimestamp_1.Timestamp; } });
var IonText_1 = require("./IonText");
Object.defineProperty(exports, "toBase64", { enumerable: true, get: function () { return IonText_1.toBase64; } });
var IonUnicode_2 = require("./IonUnicode");
Object.defineProperty(exports, "decodeUtf8", { enumerable: true, get: function () { return IonUnicode_2.decodeUtf8; } });
const dom = __importStar(require("./dom"));
exports.dom = dom;
var dom_1 = require("./dom");
Object.defineProperty(exports, "load", { enumerable: true, get: function () { return dom_1.load; } });
Object.defineProperty(exports, "loadAll", { enumerable: true, get: function () { return dom_1.loadAll; } });
var IonEvent_1 = require("./events/IonEvent");
Object.defineProperty(exports, "IonEventType", { enumerable: true, get: function () { return IonEvent_1.IonEventType; } });
Object.defineProperty(exports, "IonEventFactory", { enumerable: true, get: function () { return IonEvent_1.IonEventFactory; } });
var IonEventStream_1 = require("./events/IonEventStream");
Object.defineProperty(exports, "IonEventStream", { enumerable: true, get: function () { return IonEventStream_1.IonEventStream; } });
var EventStreamError_1 = require("./events/EventStreamError");
Object.defineProperty(exports, "EventStreamError", { enumerable: true, get: function () { return EventStreamError_1.EventStreamError; } });
var ComparisonResult_1 = require("./ComparisonResult");
Object.defineProperty(exports, "ComparisonResult", { enumerable: true, get: function () { return ComparisonResult_1.ComparisonResult; } });
Object.defineProperty(exports, "ComparisonResultType", { enumerable: true, get: function () { return ComparisonResult_1.ComparisonResultType; } });
//# sourceMappingURL=Ion.js.map