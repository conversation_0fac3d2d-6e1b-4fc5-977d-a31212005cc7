"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TB_ANNOTATION = exports.TB_STRUCT = exports.TB_SEXP = exports.TB_LIST = exports.TB_BLOB = exports.TB_CLOB = exports.TB_STRING = exports.TB_SYMBOL = exports.TB_TIMESTAMP = exports.TB_DECIMAL = exports.TB_FLOAT = exports.TB_NEG_INT = exports.TB_INT = exports.TB_BOOL = exports.TB_NULL = exports.LEN_NULL = exports.LEN_VAR = exports.LEN_MASK = exports.BYTE_SHIFT = exports.TYPE_SHIFT = exports.BYTE_MASK = exports.NIBBLE_MASK = void 0;
exports.NIBBLE_MASK = 0xf;
exports.BYTE_MASK = 0xff;
exports.TYPE_SHIFT = 4;
exports.BYTE_SHIFT = 8;
exports.LEN_MASK = 0xf;
exports.LEN_VAR = 14;
exports.LEN_NULL = 15;
exports.TB_NULL = 0;
exports.TB_BOOL = 1;
exports.TB_INT = 2;
exports.TB_NEG_INT = 3;
exports.TB_FLOAT = 4;
exports.TB_DECIMAL = 5;
exports.TB_TIMESTAMP = 6;
exports.TB_SYMBOL = 7;
exports.TB_STRING = 8;
exports.TB_CLOB = 9;
exports.TB_BLOB = 10;
exports.TB_LIST = 11;
exports.TB_SEXP = 12;
exports.TB_STRUCT = 13;
exports.TB_ANNOTATION = 14;
//# sourceMappingURL=IonBinary.js.map