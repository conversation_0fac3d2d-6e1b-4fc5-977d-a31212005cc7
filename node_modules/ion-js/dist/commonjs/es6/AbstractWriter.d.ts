/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { Reader } from "./IonReader";
import { Writer } from "./IonWriter";
export interface AbstractWriter extends Writer {
}
export declare abstract class AbstractWriter implements Writer {
    protected _annotations: string[];
    addAnnotation(annotation: string): void;
    setAnnotations(annotations: string[]): void;
    protected abstract _isInStruct(): boolean;
    writeValues(reader: Reader): void;
    writeValue(reader: Reader): void;
    protected _clearAnnotations(): void;
    private _writeValues;
    private _writeValue;
    private _validateAnnotations;
    private _isString;
}
