/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
export declare class Decimal {
    static readonly ZERO: Decimal;
    static readonly ONE: Decimal;
    private _coefficient;
    private _exponent;
    private _isNegative;
    constructor(decimalText: string);
    constructor(coefficient: number, exponent: number);
    constructor(coefficient: bigint, exponent: number, isNegative?: boolean);
    static _fromNumberCoefficient(coefficient: number, exponent: number): Decimal;
    static _fromBigIntCoefficient(isNegative: boolean, coefficient: bigint, exponent: number): Decimal;
    static parse(str: string): Decimal | null;
    isNegative(): boolean;
    numberValue(): number;
    intValue(): number;
    toString(): string;
    toJSON(): number;
    getCoefficient(): bigint;
    getExponent(): number;
    equals(that: Decimal): boolean;
    compareTo(that: Decimal): number;
    private _initialize;
    private _isNegativeZero;
    private _compareToParams;
}
