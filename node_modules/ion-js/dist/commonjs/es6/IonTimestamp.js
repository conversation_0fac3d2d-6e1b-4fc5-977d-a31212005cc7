"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Timestamp = exports.TimestampPrecision = void 0;
const IonDecimal_1 = require("./IonDecimal");
const IonText_1 = require("./IonText");
const util_1 = require("./util");
var TimestampPrecision;
(function (TimestampPrecision) {
    TimestampPrecision[TimestampPrecision["YEAR"] = 1] = "YEAR";
    TimestampPrecision[TimestampPrecision["MONTH"] = 2] = "MONTH";
    TimestampPrecision[TimestampPrecision["DAY"] = 3] = "DAY";
    TimestampPrecision[TimestampPrecision["HOUR_AND_MINUTE"] = 4] = "HOUR_AND_MINUTE";
    TimestampPrecision[TimestampPrecision["SECONDS"] = 5] = "SECONDS";
})(TimestampPrecision = exports.TimestampPrecision || (exports.TimestampPrecision = {}));
class Timestamp {
    constructor(dateOrLocalOffset = null, year = null, month = null, day = null, hour = null, minutes = null, seconds = null) {
        if (dateOrLocalOffset instanceof Date) {
            const date = dateOrLocalOffset;
            const seconds = date.getMilliseconds() === 0
                ? new IonDecimal_1.Decimal(date.getSeconds(), 0)
                : new IonDecimal_1.Decimal(date.getSeconds() * 1000 + date.getMilliseconds(), -3);
            this._localOffset = date.getTimezoneOffset() * -1;
            this._year = date.getFullYear();
            this._month = date.getMonth() + 1;
            this._day = date.getDate();
            this._hour = date.getHours();
            this._minutes = date.getMinutes();
            this._secondsDecimal = seconds;
            this._precision = TimestampPrecision.YEAR;
        }
        else {
            const localOffset = dateOrLocalOffset;
            if (localOffset === null) {
                throw new Error("Timestamp's constructor was called without localOffset");
            }
            else if (year === null) {
                throw new Error("Timestamp's constructor was called without year");
            }
            else {
                this._localOffset = localOffset;
                this._year = year;
            }
            this._precision = TimestampPrecision.YEAR;
            this._checkRequiredField("Offset", this._localOffset, Timestamp._MIN_OFFSET, Timestamp._MAX_OFFSET);
            this._checkRequiredField("Year", this._year, Timestamp._MIN_YEAR, Timestamp._MAX_YEAR);
            this._month = this._checkOptionalField("Month", month, Timestamp._MIN_MONTH, Timestamp._MAX_MONTH, 1, TimestampPrecision.MONTH);
            this._day = this._checkOptionalField("Day", day, Timestamp._MIN_DAY, Timestamp._MAX_DAY, 1, TimestampPrecision.DAY);
            this._hour = this._checkOptionalField("Hour", hour, Timestamp._MIN_HOUR, Timestamp._MAX_HOUR, 0, TimestampPrecision.HOUR_AND_MINUTE);
            this._minutes = this._checkOptionalField("Minutes", minutes, Timestamp._MIN_MINUTE, Timestamp._MAX_MINUTE, 0, TimestampPrecision.HOUR_AND_MINUTE);
            if (typeof seconds === "number") {
                if (!Number.isInteger(seconds)) {
                    throw new Error("The provided seconds number was not an integer (" + seconds + ")");
                }
                this._secondsDecimal = new IonDecimal_1.Decimal(seconds, 0);
            }
            else {
                if (seconds !== null) {
                    this._secondsDecimal = seconds;
                }
            }
        }
        if (this._secondsDecimal === null || this._secondsDecimal === undefined) {
            this._secondsDecimal = IonDecimal_1.Decimal.ZERO;
        }
        else {
            this._checkFieldRange("Seconds", this._secondsDecimal, Timestamp._MIN_SECONDS, Timestamp._MAX_SECONDS);
            this._precision = TimestampPrecision.SECONDS;
        }
        if (this._precision <= TimestampPrecision.DAY) {
            this._localOffset = -0;
        }
        if (this._precision > TimestampPrecision.MONTH) {
            const tempDate = new Date(this._year, this._month, 0);
            tempDate.setUTCFullYear(this._year);
            if (this._day > tempDate.getDate()) {
                throw new Error(`Month ${this._month} has less than ${this._day} days`);
            }
            if (this._month === 2 && this._day === 29) {
                if (!this._isLeapYear(this._year)) {
                    throw new Error(`Given February 29th but year ${this._year} is not a leap year`);
                }
            }
        }
        const utcYear = this.getDate().getUTCFullYear();
        this._checkFieldRange("Year", utcYear, Timestamp._MIN_YEAR, Timestamp._MAX_YEAR);
    }
    static parse(str) {
        return _TimestampParser._parse(str);
    }
    static _adjustMsSinceEpochIfNeeded(year, msSinceEpoch) {
        if (year >= 100) {
            return msSinceEpoch;
        }
        const date = new Date(msSinceEpoch);
        date.setUTCFullYear(year);
        return date.getTime();
    }
    static _splitSecondsDecimal(secondsDecimal) {
        const coefStr = secondsDecimal.getCoefficient().toString();
        const exp = secondsDecimal.getExponent();
        let secondsStr = "";
        let fractionStr = "";
        if (exp < 0) {
            const idx = Math.max(coefStr.length + exp, 0);
            secondsStr = coefStr.substring(0, idx);
            fractionStr = coefStr.substring(idx);
            if (-secondsDecimal.getExponent() - coefStr.length > 0) {
                fractionStr = "0".repeat(-exp - coefStr.length) + fractionStr;
            }
        }
        else if (exp > 0) {
            secondsStr = coefStr + "0".repeat(exp);
        }
        else {
            secondsStr = coefStr;
        }
        return [secondsStr, fractionStr];
    }
    static _valueOf(date, localOffset, fractionalSeconds, precision) {
        const msSinceEpoch = date.getTime() + localOffset * 60 * 1000;
        date = new Date(msSinceEpoch);
        let secondsDecimal;
        if (fractionalSeconds != null) {
            const [_, fractionStr] = Timestamp._splitSecondsDecimal(fractionalSeconds);
            secondsDecimal = IonDecimal_1.Decimal.parse(date.getUTCSeconds() + "." + fractionStr);
        }
        else {
            secondsDecimal = IonDecimal_1.Decimal.parse(date.getUTCSeconds() + "." + date.getUTCMilliseconds());
        }
        switch (precision) {
            case TimestampPrecision.YEAR:
                return new Timestamp(localOffset, date.getUTCFullYear());
            case TimestampPrecision.MONTH:
                return new Timestamp(localOffset, date.getUTCFullYear(), date.getUTCMonth() + 1);
            case TimestampPrecision.DAY:
                return new Timestamp(localOffset, date.getUTCFullYear(), date.getUTCMonth() + 1, date.getUTCDate());
            case TimestampPrecision.HOUR_AND_MINUTE:
                return new Timestamp(localOffset, date.getUTCFullYear(), date.getUTCMonth() + 1, date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes());
            case TimestampPrecision.SECONDS:
            default:
                return new Timestamp(localOffset, date.getUTCFullYear(), date.getUTCMonth() + 1, date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), secondsDecimal);
        }
    }
    getLocalOffset() {
        return this._localOffset;
    }
    getPrecision() {
        return this._precision;
    }
    getDate() {
        let ms = 0;
        if (this._precision === TimestampPrecision.SECONDS) {
            ms = Math.round((this._secondsDecimal.numberValue() - this.getSecondsInt()) * 1000);
        }
        let msSinceEpoch = Date.UTC(this._year, this._precision === TimestampPrecision.YEAR ? 0 : this._month - 1, this._day, this._hour, this._minutes, this.getSecondsInt(), ms);
        msSinceEpoch = Timestamp._adjustMsSinceEpochIfNeeded(this._year, msSinceEpoch);
        const offsetShiftMs = this._localOffset * 60 * 1000;
        return new Date(msSinceEpoch - offsetShiftMs);
    }
    getSecondsInt() {
        return this._secondsDecimal.intValue();
    }
    getSecondsDecimal() {
        return this._secondsDecimal;
    }
    _getFractionalSeconds() {
        const [_, fractionStr] = Timestamp._splitSecondsDecimal(this._secondsDecimal);
        if (fractionStr === "") {
            return IonDecimal_1.Decimal.ZERO;
        }
        return IonDecimal_1.Decimal.parse(fractionStr + "d-" + fractionStr.length);
    }
    equals(that) {
        return (this.getPrecision() === that.getPrecision() &&
            this.getLocalOffset() === that.getLocalOffset() &&
            util_1._sign(this.getLocalOffset()) === util_1._sign(that.getLocalOffset()) &&
            this.compareTo(that) === 0 &&
            this._secondsDecimal.equals(that._secondsDecimal));
    }
    compareTo(that) {
        const thisMs = this.getDate().getTime();
        const thatMs = that.getDate().getTime();
        if (thisMs === thatMs) {
            return this.getSecondsDecimal().compareTo(that.getSecondsDecimal());
        }
        return thisMs < thatMs ? -1 : 1;
    }
    toString() {
        let strVal = "";
        switch (this._precision) {
            default:
                throw new Error("unrecognized timestamp precision " + this._precision);
            case TimestampPrecision.SECONDS:
                const [secondsStr, fractionStr] = Timestamp._splitSecondsDecimal(this._secondsDecimal);
                strVal = this._lpadZeros(secondsStr, 2);
                if (fractionStr.length > 0) {
                    strVal += "." + fractionStr;
                }
            case TimestampPrecision.HOUR_AND_MINUTE:
                strVal =
                    this._lpadZeros(this._minutes, 2) + (strVal ? ":" + strVal : "");
                strVal = this._lpadZeros(this._hour, 2) + (strVal ? ":" + strVal : "");
            case TimestampPrecision.DAY:
                strVal = this._lpadZeros(this._day, 2) + (strVal ? "T" + strVal : "T");
            case TimestampPrecision.MONTH:
                strVal =
                    this._lpadZeros(this._month, 2) + (strVal ? "-" + strVal : "");
            case TimestampPrecision.YEAR:
                if (this._precision === TimestampPrecision.YEAR) {
                    strVal = this._lpadZeros(this._year, 4) + "T";
                }
                else if (this._precision === TimestampPrecision.MONTH) {
                    strVal = this._lpadZeros(this._year, 4) + "-" + strVal + "T";
                }
                else {
                    strVal = this._lpadZeros(this._year, 4) + "-" + strVal;
                }
        }
        const o = this._localOffset;
        if (this._precision > TimestampPrecision.DAY) {
            if (o === 0 && util_1._sign(o) === 1) {
                strVal = strVal + "Z";
            }
            else {
                strVal +=
                    (util_1._sign(o) === -1 ? "-" : "+") +
                        this._lpadZeros(Math.floor(Math.abs(o) / 60), 2) +
                        ":" +
                        this._lpadZeros(Math.abs(o) % 60, 2);
            }
        }
        return strVal;
    }
    toJSON() {
        return this.getDate().toISOString();
    }
    _checkRequiredField(fieldName, value, min, max) {
        if (!util_1._hasValue(value)) {
            throw new Error(`${fieldName} cannot be ${value}`);
        }
        this._checkFieldRange(fieldName, value, min, max);
    }
    _checkOptionalField(fieldName, value, min, max, defaultValue, precision) {
        if (!util_1._hasValue(value)) {
            return defaultValue;
        }
        this._checkFieldRange(fieldName, value, min, max);
        this._precision = precision;
        return value;
    }
    _checkFieldRange(fieldName, value, min, max) {
        if (value instanceof IonDecimal_1.Decimal) {
            if (util_1._hasValue(value) &&
                (value.compareTo(min) < 0 ||
                    value.compareTo(max) >= 0)) {
                throw new Error(`${fieldName} ${value} must be between ${min} inclusive, and ${max} exclusive`);
            }
        }
        else {
            if (!Number.isInteger(value)) {
                throw new Error(`${fieldName} ${value} must be an integer`);
            }
            if (value < min || value > max) {
                throw new Error(`${fieldName} ${value} must be between ${min} and ${max} inclusive`);
            }
        }
    }
    _isLeapYear(year) {
        if (year % 4 !== 0) {
            return false;
        }
        if (year % 400 === 0) {
            return true;
        }
        if (year % 100 === 0) {
            return year < 1600;
        }
        return true;
    }
    _lpadZeros(v, size) {
        const s = v.toString();
        if (s.length <= size) {
            return "0".repeat(size - s.length) + s;
        }
        throw new Error("Unable to fit '" + s + "' into " + size + " characters");
    }
}
exports.Timestamp = Timestamp;
Timestamp._MIN_SECONDS = IonDecimal_1.Decimal.ZERO;
Timestamp._MAX_SECONDS = IonDecimal_1.Decimal.parse("60");
Timestamp._MIN_MINUTE = 0;
Timestamp._MAX_MINUTE = 59;
Timestamp._MIN_HOUR = 0;
Timestamp._MAX_HOUR = 23;
Timestamp._MIN_DAY = 1;
Timestamp._MAX_DAY = 31;
Timestamp._MIN_MONTH = 1;
Timestamp._MAX_MONTH = 12;
Timestamp._MIN_YEAR = 1;
Timestamp._MAX_YEAR = 9999;
Timestamp._MIN_OFFSET = -23 * 60 - 59;
Timestamp._MAX_OFFSET = 23 * 60 + 59;
var _States;
(function (_States) {
    _States[_States["YEAR"] = 0] = "YEAR";
    _States[_States["MONTH"] = 1] = "MONTH";
    _States[_States["DAY"] = 2] = "DAY";
    _States[_States["HOUR"] = 3] = "HOUR";
    _States[_States["MINUTE"] = 4] = "MINUTE";
    _States[_States["SECONDS"] = 5] = "SECONDS";
    _States[_States["FRACTIONAL_SECONDS"] = 6] = "FRACTIONAL_SECONDS";
    _States[_States["OFFSET_POSITIVE"] = 7] = "OFFSET_POSITIVE";
    _States[_States["OFFSET_NEGATIVE"] = 8] = "OFFSET_NEGATIVE";
    _States[_States["OFFSET_MINUTES"] = 9] = "OFFSET_MINUTES";
    _States[_States["OFFSET_ZULU"] = 10] = "OFFSET_ZULU";
    _States[_States["OFFSET_UNKNOWN"] = 11] = "OFFSET_UNKNOWN";
})(_States || (_States = {}));
class _TimeParserState {
    constructor(f, len, t = null) {
        this.f = f;
        this.len = len;
        this.t = t;
    }
}
class _TimestampParser {
    static _parse(str) {
        if (str.length < 1) {
            return null;
        }
        if (str.charCodeAt(0) === 110) {
            if (str === "null" || str === "null.timestamp") {
                return null;
            }
            throw new Error("Illegal timestamp: " + str);
        }
        let offsetSign;
        let offset = null;
        let year = 0;
        let month = null;
        let day = null;
        let hour = null;
        let minute = null;
        let secondsInt = null;
        let fractionStr = "";
        let pos = 0;
        let state = _TimestampParser._timeParserStates[_States.YEAR];
        const limit = str.length;
        let v;
        while (pos < limit) {
            if (state.len === null) {
                const digits = _TimestampParser._readUnknownDigits(str, pos);
                if (digits.length === 0) {
                    throw new Error("No digits found at pos: " + pos);
                }
                v = parseInt(digits, 10);
                pos += digits.length;
            }
            else if (state.len > 0) {
                v = _TimestampParser._readDigits(str, pos, state.len);
                if (v < 0) {
                    throw new Error("Non-digit value found at pos " + pos);
                }
                pos = pos + state.len;
            }
            v = v;
            switch (state.f) {
                case _States.YEAR:
                    year = v;
                    break;
                case _States.MONTH:
                    month = v;
                    break;
                case _States.DAY:
                    day = v;
                    break;
                case _States.HOUR:
                    hour = v;
                    break;
                case _States.MINUTE:
                    minute = v;
                    break;
                case _States.SECONDS:
                    secondsInt = v;
                    break;
                case _States.FRACTIONAL_SECONDS:
                    fractionStr = str.substring(20, pos);
                    break;
                case _States.OFFSET_POSITIVE:
                    offsetSign = 1;
                    offset = v * 60;
                    break;
                case _States.OFFSET_NEGATIVE:
                    offsetSign = -1;
                    offset = v * 60;
                    break;
                case _States.OFFSET_MINUTES:
                    offset += v;
                    if (v >= 60) {
                        throw new Error("Minute offset " + String(v) + " above maximum or equal to : 60");
                    }
                    break;
                case _States.OFFSET_ZULU:
                    offsetSign = 1;
                    offset = 0;
                    break;
                case _States.OFFSET_UNKNOWN:
                    offset = -0;
                    break;
                default:
                    throw new Error("invalid internal state");
            }
            if (pos >= limit) {
                break;
            }
            if (state.t !== null) {
                const c = String.fromCharCode(str.charCodeAt(pos));
                state = _TimestampParser._timeParserStates[state.t[c]];
                if (state === undefined) {
                    throw new Error("State was not set pos:" + pos);
                }
                if (state.f === _States.OFFSET_ZULU) {
                    offsetSign = 1;
                    offset = 0;
                }
            }
            pos++;
        }
        if (offset === null) {
            if (minute !== null) {
                throw new Error('invalid timestamp, missing local offset: "' + str + '"');
            }
            offset = -0;
        }
        else {
            offset = offsetSign * offset;
        }
        let seconds;
        if ((secondsInt !== undefined && secondsInt !== null) || fractionStr) {
            seconds = IonDecimal_1.Decimal.parse(secondsInt + "." + (fractionStr ? fractionStr : ""));
        }
        return new Timestamp(offset, year, month, day, hour, minute, seconds);
    }
    static _readUnknownDigits(str, pos) {
        let i = pos;
        for (; i < str.length; i++) {
            if (!IonText_1.isDigit(str.charCodeAt(i))) {
                break;
            }
        }
        return str.substring(pos, i);
    }
    static _readDigits(str, pos, len) {
        let v = 0;
        for (let i = pos; i < pos + len; i++) {
            const c = str.charCodeAt(i) - 48;
            if (c < 0 && c > 9) {
                return -1;
            }
            v = v * 10 + c;
        }
        return v;
    }
}
_TimestampParser._timeParserStates = {
    [_States.YEAR]: new _TimeParserState(_States.YEAR, 4, {
        T: _States.OFFSET_UNKNOWN,
        "-": _States.MONTH,
    }),
    [_States.MONTH]: new _TimeParserState(_States.MONTH, 2, {
        T: _States.OFFSET_UNKNOWN,
        "-": _States.DAY,
    }),
    [_States.DAY]: new _TimeParserState(_States.DAY, 2, { T: _States.HOUR }),
    [_States.HOUR]: new _TimeParserState(_States.HOUR, 2, {
        ":": _States.MINUTE,
    }),
    [_States.MINUTE]: new _TimeParserState(_States.MINUTE, 2, {
        ":": _States.SECONDS,
        "+": _States.OFFSET_POSITIVE,
        "-": _States.OFFSET_NEGATIVE,
        Z: _States.OFFSET_ZULU,
    }),
    [_States.SECONDS]: new _TimeParserState(_States.SECONDS, 2, {
        ".": _States.FRACTIONAL_SECONDS,
        "+": _States.OFFSET_POSITIVE,
        "-": _States.OFFSET_NEGATIVE,
        Z: _States.OFFSET_ZULU,
    }),
    [_States.FRACTIONAL_SECONDS]: new _TimeParserState(_States.FRACTIONAL_SECONDS, null, {
        "+": _States.OFFSET_POSITIVE,
        "-": _States.OFFSET_NEGATIVE,
        Z: _States.OFFSET_ZULU,
    }),
    [_States.OFFSET_POSITIVE]: new _TimeParserState(_States.OFFSET_POSITIVE, 2, { ":": _States.OFFSET_MINUTES }),
    [_States.OFFSET_NEGATIVE]: new _TimeParserState(_States.OFFSET_NEGATIVE, 2, { ":": _States.OFFSET_MINUTES }),
    [_States.OFFSET_MINUTES]: new _TimeParserState(_States.OFFSET_MINUTES, 2),
    [_States.OFFSET_ZULU]: new _TimeParserState(_States.OFFSET_ZULU, 0),
    [_States.OFFSET_UNKNOWN]: new _TimeParserState(_States.OFFSET_UNKNOWN, 0),
};
//# sourceMappingURL=IonTimestamp.js.map