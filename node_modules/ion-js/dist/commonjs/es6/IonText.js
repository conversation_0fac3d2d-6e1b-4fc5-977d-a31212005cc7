"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.escape = exports.isDigit = exports.isOperator = exports.isIdentifier = exports.SymbolEscapes = exports.StringEscapes = exports.ClobEscapes = exports.CharCodes = exports.toBase64 = exports.fromBase64 = exports.is_hex_digit = exports.is_base64_char = exports.is_whitespace = exports.is_operator_char = exports.is_letter_or_digit = exports.isNumericTerminator = exports.is_letter = exports.toHex = exports.escapeSequence = exports.escapeString = exports.needsEscape = exports.nextEscape = exports.asAscii = exports.is_keyword = exports.is_digit = exports.ESCAPED_NEWLINE = exports.WHITESPACE_COMMENT2 = exports.WHITESPACE_COMMENT1 = void 0;
exports.WHITESPACE_COMMENT1 = -2;
exports.WHITESPACE_COMMENT2 = -3;
exports.ESCAPED_NEWLINE = -4;
const DOUBLE_QUOTE = 34;
const SINGLE_QUOTE = 39;
const SLASH = 92;
const _escapeStrings = {
    0: "\\0",
    8: "\\b",
    9: "\\t",
    10: "\\n",
    13: "\\r",
    DOUBLE_QUOTE: '\\"',
    SINGLE_QUOTE: "\\'",
    SLASH: "\\\\",
};
function _make_bool_array(str) {
    let i = str.length;
    const a = [];
    a[128] = false;
    while (i > 0) {
        --i;
        a[str.charCodeAt(i)] = true;
    }
    return a;
}
const _is_base64_char = _make_bool_array("+/0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ");
const _is_hex_digit = _make_bool_array("0123456789abcdefABCDEF");
const _is_letter = _make_bool_array("_$abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ");
const _is_letter_or_digit = _make_bool_array("_$0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ");
const _is_numeric_terminator = _make_bool_array("{}[](),\"' \t\n\r\v\u000c");
const _is_operator_char = _make_bool_array("!#%&*+-./;<=>?@^`|~");
const _is_whitespace = _make_bool_array(" \t\r\n\u000b\u000c");
const isIdentifierArray = _make_bool_array("_$0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ");
function is_digit(ch) {
    if (ch < 48 || ch > 57) {
        return false;
    }
    return true;
}
exports.is_digit = is_digit;
function is_keyword(str) {
    return (str === "null" ||
        str === "true" ||
        str === "false" ||
        str === "nan" ||
        str === "+inf" ||
        str === "-inf");
}
exports.is_keyword = is_keyword;
function asAscii(s) {
    if (typeof s === "undefined") {
        s = "undefined::null";
    }
    else if (typeof s == "number") {
        s = "" + s;
    }
    else if (typeof s != "string") {
        const esc = nextEscape(s, s.length);
        if (esc >= 0) {
            s = escapeString(s, esc);
        }
    }
    return s;
}
exports.asAscii = asAscii;
function nextEscape(s, prev) {
    while (prev-- > 0) {
        if (needsEscape(s.charCodeAt(prev))) {
            break;
        }
    }
    return prev;
}
exports.nextEscape = nextEscape;
function needsEscape(c) {
    if (c < 32) {
        return true;
    }
    if (c > 126) {
        return true;
    }
    if (c === DOUBLE_QUOTE || c === SINGLE_QUOTE || c === SLASH) {
        return true;
    }
    return false;
}
exports.needsEscape = needsEscape;
function escapeString(s, pos) {
    const fixes = [];
    let c, ii, s2;
    while (pos >= 0) {
        c = s.charCodeAt(pos);
        if (!needsEscape(c)) {
            break;
        }
        fixes.push([pos, c]);
        pos = nextEscape(s, pos);
    }
    if (fixes.length > 0) {
        s2 = "";
        ii = fixes.length;
        pos = s.length;
        while (ii--) {
            const fix = fixes[ii];
            const tail_len = pos - fix[0] - 1;
            if (tail_len > 0) {
                s2 = escapeSequence(fix[1]) + s.substring(fix[0] + 1, pos) + s2;
            }
            else {
                s2 = s.substring(fix[0] + 1, pos) + s2;
            }
            pos = fix[0] - 1;
        }
        if (pos >= 0) {
            s2 = s.substring(0, pos) + s2;
        }
        s = s2;
    }
    return s;
}
exports.escapeString = escapeString;
function escapeSequence(c) {
    let s = _escapeStrings[c];
    if (typeof s === "undefined") {
        if (c < 256) {
            s = "\\x" + toHex(c, 2);
        }
        else if (c <= 0xffff) {
            s = "\\u" + toHex(c, 4);
        }
        else {
            s = "\\U" + toHex(c, 8);
        }
    }
    return s;
}
exports.escapeSequence = escapeSequence;
function toHex(c, len) {
    let s = "";
    while (c > 0) {
        s += "0123456789ABCDEF".charAt(c && 0xf);
        c = c / 16;
    }
    if (s.length < len) {
        s = "000000000" + s;
        s = s.substring(s.length - len, s.length);
    }
    return s;
}
exports.toHex = toHex;
function is_letter(ch) {
    return _is_letter[ch];
}
exports.is_letter = is_letter;
function isNumericTerminator(ch) {
    if (ch == -1) {
        return true;
    }
    return _is_numeric_terminator[ch];
}
exports.isNumericTerminator = isNumericTerminator;
function is_letter_or_digit(ch) {
    return _is_letter_or_digit[ch];
}
exports.is_letter_or_digit = is_letter_or_digit;
function is_operator_char(ch) {
    return _is_operator_char[ch];
}
exports.is_operator_char = is_operator_char;
function is_whitespace(ch) {
    if (ch > 32) {
        return false;
    }
    if (ch == exports.WHITESPACE_COMMENT1) {
        return true;
    }
    if (ch == exports.WHITESPACE_COMMENT2) {
        return true;
    }
    if (ch == exports.ESCAPED_NEWLINE) {
        return true;
    }
    return _is_whitespace[ch];
}
exports.is_whitespace = is_whitespace;
function is_base64_char(ch) {
    return _is_base64_char[ch];
}
exports.is_base64_char = is_base64_char;
function is_hex_digit(ch) {
    return _is_hex_digit[ch];
}
exports.is_hex_digit = is_hex_digit;
const base64chars = [
    "A",
    "B",
    "C",
    "D",
    "E",
    "F",
    "G",
    "H",
    "I",
    "J",
    "K",
    "L",
    "M",
    "N",
    "O",
    "P",
    "Q",
    "R",
    "S",
    "T",
    "U",
    "V",
    "W",
    "X",
    "Y",
    "Z",
    "a",
    "b",
    "c",
    "d",
    "e",
    "f",
    "g",
    "h",
    "i",
    "j",
    "k",
    "l",
    "m",
    "n",
    "o",
    "p",
    "q",
    "r",
    "s",
    "t",
    "u",
    "v",
    "w",
    "x",
    "y",
    "z",
    "0",
    "1",
    "2",
    "3",
    "4",
    "5",
    "6",
    "7",
    "8",
    "9",
    "+",
    "/",
];
const base64inv = {
    A: 0,
    B: 1,
    C: 2,
    D: 3,
    E: 4,
    F: 5,
    G: 6,
    H: 7,
    I: 8,
    J: 9,
    K: 10,
    L: 11,
    M: 12,
    N: 13,
    O: 14,
    P: 15,
    Q: 16,
    R: 17,
    S: 18,
    T: 19,
    U: 20,
    V: 21,
    W: 22,
    X: 23,
    Y: 24,
    Z: 25,
    a: 26,
    b: 27,
    c: 28,
    d: 29,
    e: 30,
    f: 31,
    g: 32,
    h: 33,
    i: 34,
    j: 35,
    k: 36,
    l: 37,
    m: 38,
    n: 39,
    o: 40,
    p: 41,
    q: 42,
    r: 43,
    s: 44,
    t: 45,
    u: 46,
    v: 47,
    w: 48,
    x: 49,
    y: 50,
    z: 51,
    "0": 52,
    "1": 53,
    "2": 54,
    "3": 55,
    "4": 56,
    "5": 57,
    "6": 58,
    "7": 59,
    "8": 60,
    "9": 61,
    "+": 62,
    "/": 63,
};
function fromBase64(str) {
    let pad = 0;
    for (let i = str.length - 1; str.charAt(i) == "="; i--) {
        pad++;
    }
    const buf = new Uint8Array((str.length * 3) / 4 - pad);
    for (let i = 0; i < str.length - pad; i += 4) {
        const c0 = base64inv[str.charAt(i)], c1 = base64inv[str.charAt(i + 1)], c2 = base64inv[str.charAt(i + 2)], c3 = base64inv[str.charAt(i + 3)];
        buf[(i * 3) / 4] = ((c0 << 2) & 255) | (c1 >>> 4);
        if (i + 2 < str.length - pad) {
            buf[(i * 3) / 4 + 1] = ((c1 << 4) & 255) | (c2 >>> 2);
            if (i + 3 < str.length - pad) {
                buf[(i * 3) / 4 + 2] = ((c2 << 6) & 255) | c3;
            }
        }
    }
    return buf;
}
exports.fromBase64 = fromBase64;
function toBase64(buf) {
    const str = new Array(Math.ceil((buf.length * 4) / 3));
    for (let i = 0; i < buf.length; i += 3) {
        const b0 = buf[i], b1 = buf[i + 1], b2 = buf[i + 2], b3 = buf[i + 3];
        str[(i * 4) / 3] = base64chars[b0 >>> 2];
        str[(i * 4) / 3 + 1] = base64chars[((b0 << 4) & 63) | ((b1 || 0) >>> 4)];
        if (i + 1 < buf.length) {
            str[(i * 4) / 3 + 2] = base64chars[((b1 << 2) & 63) | ((b2 || 0) >>> 6)];
            if (i + 2 < buf.length) {
                str[(i * 4) / 3 + 3] = base64chars[b2 & 63];
            }
            else {
                return str.join("") + "=";
            }
        }
        else {
            return str.join("") + "==";
        }
    }
    return str.join("");
}
exports.toBase64 = toBase64;
var CharCodes;
(function (CharCodes) {
    CharCodes[CharCodes["NULL"] = 0] = "NULL";
    CharCodes[CharCodes["BELL"] = 7] = "BELL";
    CharCodes[CharCodes["BACKSPACE"] = 8] = "BACKSPACE";
    CharCodes[CharCodes["HORIZONTAL_TAB"] = 9] = "HORIZONTAL_TAB";
    CharCodes[CharCodes["LINE_FEED"] = 10] = "LINE_FEED";
    CharCodes[CharCodes["VERTICAL_TAB"] = 11] = "VERTICAL_TAB";
    CharCodes[CharCodes["FORM_FEED"] = 12] = "FORM_FEED";
    CharCodes[CharCodes["CARRIAGE_RETURN"] = 13] = "CARRIAGE_RETURN";
    CharCodes[CharCodes["DOUBLE_QUOTE"] = 34] = "DOUBLE_QUOTE";
    CharCodes[CharCodes["SINGLE_QUOTE"] = 39] = "SINGLE_QUOTE";
    CharCodes[CharCodes["FORWARD_SLASH"] = 47] = "FORWARD_SLASH";
    CharCodes[CharCodes["QUESTION_MARK"] = 63] = "QUESTION_MARK";
    CharCodes[CharCodes["BACKSLASH"] = 92] = "BACKSLASH";
    CharCodes[CharCodes["LEFT_PARENTHESIS"] = 40] = "LEFT_PARENTHESIS";
    CharCodes[CharCodes["RIGHT_PARENTHESIS"] = 41] = "RIGHT_PARENTHESIS";
    CharCodes[CharCodes["LEFT_BRACE"] = 123] = "LEFT_BRACE";
    CharCodes[CharCodes["RIGHT_BRACE"] = 125] = "RIGHT_BRACE";
    CharCodes[CharCodes["LEFT_BRACKET"] = 91] = "LEFT_BRACKET";
    CharCodes[CharCodes["RIGHT_BRACKET"] = 93] = "RIGHT_BRACKET";
    CharCodes[CharCodes["COMMA"] = 44] = "COMMA";
    CharCodes[CharCodes["SPACE"] = 32] = "SPACE";
    CharCodes[CharCodes["LOWERCASE_X"] = 120] = "LOWERCASE_X";
    CharCodes[CharCodes["COLON"] = 58] = "COLON";
})(CharCodes = exports.CharCodes || (exports.CharCodes = {}));
function backslashEscape(s) {
    return [CharCodes.BACKSLASH, s.charCodeAt(0)];
}
function toCharCodes(s) {
    const charCodes = new Array(s.length);
    for (let i = 0; i < s.length; i++) {
        charCodes[i] = s.charCodeAt(i);
    }
    return charCodes;
}
const _HEX_ESCAPE_PREFIX = [CharCodes.BACKSLASH, CharCodes.LOWERCASE_X];
function hexEscape(codePoint) {
    let hexEscape = codePoint.toString(16);
    while (hexEscape.length < 2) {
        hexEscape = "0" + hexEscape;
    }
    return _HEX_ESCAPE_PREFIX.concat(toCharCodes(hexEscape));
}
function populateWithHexEscapes(escapes, start, end) {
    if (end === undefined) {
        escapes[start] = hexEscape(start);
    }
    else {
        for (let i = start; i < end; i++) {
            escapes[i] = hexEscape(i);
        }
    }
}
const CommonEscapes = {};
CommonEscapes[CharCodes.NULL] = backslashEscape("0");
populateWithHexEscapes(CommonEscapes, 1, 7);
CommonEscapes[CharCodes.BELL] = backslashEscape("a");
CommonEscapes[CharCodes.BACKSPACE] = backslashEscape("b");
CommonEscapes[CharCodes.HORIZONTAL_TAB] = backslashEscape("t");
CommonEscapes[CharCodes.LINE_FEED] = backslashEscape("n");
CommonEscapes[CharCodes.VERTICAL_TAB] = backslashEscape("v");
CommonEscapes[CharCodes.FORM_FEED] = backslashEscape("f");
CommonEscapes[CharCodes.CARRIAGE_RETURN] = backslashEscape("r");
populateWithHexEscapes(CommonEscapes, 14, 32);
CommonEscapes[CharCodes.BACKSLASH] = backslashEscape("\\");
populateWithHexEscapes(CommonEscapes, 0x7f, 0xa0);
exports.ClobEscapes = Object["assign"]({}, CommonEscapes);
exports.ClobEscapes[CharCodes.DOUBLE_QUOTE] = backslashEscape('"');
exports.ClobEscapes[CharCodes.SINGLE_QUOTE] = backslashEscape("'");
exports.ClobEscapes[CharCodes.FORWARD_SLASH] = backslashEscape("/");
exports.ClobEscapes[CharCodes.QUESTION_MARK] = backslashEscape("?");
exports.StringEscapes = Object["assign"]({}, CommonEscapes);
exports.StringEscapes[CharCodes.DOUBLE_QUOTE] = backslashEscape('"');
exports.SymbolEscapes = Object["assign"]({}, CommonEscapes);
exports.SymbolEscapes[CharCodes.SINGLE_QUOTE] = backslashEscape("'");
function isIdentifier(s) {
    if (is_digit(s.charCodeAt(0))) {
        return false;
    }
    for (let i = 0; i < s.length; i++) {
        const c = s.charCodeAt(i);
        const b = isIdentifierArray[c];
        if (!b) {
            return false;
        }
    }
    return true;
}
exports.isIdentifier = isIdentifier;
function isOperator(s) {
    for (let i = 0; i < s.length; i++) {
        const c = s.charCodeAt(i);
        const b = _is_operator_char[c];
        if (!b) {
            return false;
        }
    }
    return true;
}
exports.isOperator = isOperator;
function isDigit(charCode) {
    return charCode < 58 && charCode > 47;
}
exports.isDigit = isDigit;
function escape(input, escapes) {
    let escapedString = "";
    let escapeSeq = "";
    let charCode;
    let escape;
    let lastIndex = 0;
    for (let i = 0; i < input.length; i++) {
        charCode = input.charCodeAt(i);
        escape = escapes[charCode];
        if (escape !== undefined) {
            for (let j = 0; j < escape.length; j++) {
                escapeSeq += String.fromCharCode(escape[j]);
            }
            escapedString += input.slice(lastIndex, i) + escapeSeq;
            lastIndex = i + 1;
            escapeSeq = "";
        }
    }
    return escapedString + input.slice(lastIndex, input.length);
}
exports.escape = escape;
//# sourceMappingURL=IonText.js.map