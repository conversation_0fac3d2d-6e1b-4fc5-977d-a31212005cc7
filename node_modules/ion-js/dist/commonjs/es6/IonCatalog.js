"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Catalog = void 0;
const IonSystemSymbolTable_1 = require("./IonSystemSymbolTable");
function byVersion(x, y) {
    return x.version - y.version;
}
class Catalog {
    constructor() {
        this.symbolTables = {};
        this.add(IonSystemSymbolTable_1.getSystemSymbolTable());
    }
    add(symbolTable) {
        if (symbolTable.name === undefined || symbolTable.name === null) {
            throw new Error("SymbolTable name must be defined.");
        }
        const versions = this.symbolTables[symbolTable.name];
        if (versions === undefined) {
            this.symbolTables[symbolTable.name] = [];
        }
        this.symbolTables[symbolTable.name][symbolTable.version] = symbolTable;
    }
    getVersion(name, version) {
        const tables = this.symbolTables[name];
        if (!tables) {
            return null;
        }
        let table = tables[version];
        if (!table) {
            table = tables[tables.length];
        }
        return table ? table : null;
    }
    getTable(name) {
        const versions = this.symbolTables[name];
        if (versions === undefined) {
            return null;
        }
        return versions[versions.length - 1];
    }
}
exports.Catalog = Catalog;
//# sourceMappingURL=IonCatalog.js.map