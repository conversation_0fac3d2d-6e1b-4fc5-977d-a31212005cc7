"use strict";
/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.BigIntSerde = void 0;
class BigIntSerde {
    static toSignedIntBytes(value, isNegative) {
        let bytes = this.toUnsignedIntBytes(value);
        if (bytes[0] >= 128) {
            const extendedBytes = new Uint8Array(bytes.length + 1);
            extendedBytes.set(bytes, 1);
            bytes = extendedBytes;
        }
        if (isNegative) {
            bytes[0] += 0x80;
        }
        return bytes;
    }
    static fromUnsignedBytes(bytes) {
        let magnitude = 0n;
        for (let m = 0; m < bytes.length; m++) {
            const byte = BigInt(bytes[m]);
            magnitude = magnitude << this.BITS_PER_BYTE;
            magnitude = magnitude | byte;
        }
        return magnitude;
    }
    static toUnsignedIntBytes(value) {
        if (value < 0n) {
            value = -value;
        }
        const sizeInBytes = this.getUnsignedIntSizeInBytes(value);
        const bytes = new Uint8Array(sizeInBytes);
        for (let m = sizeInBytes - 1; m >= 0; m--) {
            const lastByte = Number(value & this.BYTE_MAX_VALUE);
            value = value >> this.BITS_PER_BYTE;
            bytes[m] = lastByte;
        }
        return bytes;
    }
    static getUnsignedIntSizeInBytes(value) {
        for (let m = 0; m < this.SIZE_THRESHOLDS.length; m++) {
            const threshold = this.SIZE_THRESHOLDS[m];
            if (value <= threshold) {
                return m + 1;
            }
        }
        let sizeInBytes = this.SIZE_THRESHOLDS.length;
        let threshold = this.calculateSizeThreshold(sizeInBytes);
        while (value > threshold) {
            sizeInBytes++;
            threshold = this.calculateSizeThreshold(sizeInBytes);
        }
        return sizeInBytes;
    }
    static calculateSizeThresholds() {
        const thresholds = [];
        for (let m = 1; m <= this.SERIALIZED_BIGINT_SIZES_TO_PRECOMPUTE; m++) {
            thresholds.push(this.calculateSizeThreshold(m));
        }
        return thresholds;
    }
    static calculateSizeThreshold(numberOfBytes) {
        const exponent = BigInt(numberOfBytes) * this.BITS_PER_BYTE;
        const threshold = 2n ** exponent;
        return threshold - 1n;
    }
}
exports.BigIntSerde = BigIntSerde;
BigIntSerde.SERIALIZED_BIGINT_SIZES_TO_PRECOMPUTE = 64;
BigIntSerde.BITS_PER_BYTE = 8n;
BigIntSerde.BYTE_MAX_VALUE = BigInt(0xff);
BigIntSerde.SIZE_THRESHOLDS = BigIntSerde.calculateSizeThresholds();
//# sourceMappingURL=BigIntSerde.js.map