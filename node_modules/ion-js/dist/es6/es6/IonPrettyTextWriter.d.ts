/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { TextWriter } from "./IonTextWriter";
import { IonType } from "./IonType";
import { Writeable } from "./IonWriteable";
declare type Serializer<T> = (value: T) => void;
export declare class PrettyTextWriter extends TextWriter {
    private readonly indentSize;
    private indentCount;
    constructor(writeable: Writeable, indentSize?: number);
    writeFieldName(fieldName: string): void;
    writeNull(type: IonType): void;
    stepOut(): void;
    _serializeValue<T>(type: IonType, value: T, serialize: Serializer<T>): void;
    writeContainer(type: IonType, openingCharacter: number): void;
    handleSeparator(): void;
    private writePrettyValue;
    private writePrettyNewLine;
    private writePrettyIndent;
}
export {};
