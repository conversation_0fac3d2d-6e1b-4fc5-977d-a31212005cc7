/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import IntSize from "./IntSize";
import { BinaryReader } from "./IonBinaryReader";
import { BinaryWriter } from "./IonBinaryWriter";
import { IVM } from "./IonConstants";
import { defaultLocalSymbolTable } from "./IonLocalSymbolTable";
import { PrettyTextWriter } from "./IonPrettyTextWriter";
import { BinarySpan, StringSpan } from "./IonSpan";
import { TextReader } from "./IonTextReader";
import { TextWriter } from "./IonTextWriter";
import { decodeUtf8 } from "./IonUnicode";
import { Writeable } from "./IonWriteable";
function isBinary(buffer) {
    if (buffer.length < 4) {
        return false;
    }
    for (let i = 0; i < 4; i++) {
        if (buffer[i] !== IVM.binary[i]) {
            return false;
        }
    }
    return true;
}
export function makeReader(buf, catalog) {
    if (typeof buf === "string") {
        return new TextReader(new StringSpan(buf), catalog);
    }
    const bufArray = new Uint8Array(buf);
    if (isBinary(bufArray)) {
        return new BinaryReader(new BinarySpan(bufArray), catalog);
    }
    else {
        return new TextReader(new StringSpan(decodeUtf8(bufArray)), catalog);
    }
}
export function makeTextWriter() {
    return new TextWriter(new Writeable());
}
export function makePrettyWriter(indentSize) {
    return new PrettyTextWriter(new Writeable(), indentSize);
}
export function makeBinaryWriter() {
    const localSymbolTable = defaultLocalSymbolTable();
    return new BinaryWriter(localSymbolTable, new Writeable());
}
function _writeAllTo(writer, values) {
    for (const value of values) {
        dom.Value.from(value).writeTo(writer);
    }
    writer.close();
    return writer.getBytes();
}
export function dumpBinary(...values) {
    return _writeAllTo(makeBinaryWriter(), values);
}
export function dumpText(...values) {
    return decodeUtf8(_writeAllTo(makeTextWriter(), values));
}
export function dumpPrettyText(...values) {
    return decodeUtf8(_writeAllTo(makePrettyWriter(), values));
}
export { Catalog } from "./IonCatalog";
export { Decimal } from "./IonDecimal";
export { defaultLocalSymbolTable } from "./IonLocalSymbolTable";
export { IntSize };
export { IonType } from "./IonType";
export { IonTypes } from "./IonTypes";
export { SharedSymbolTable } from "./IonSharedSymbolTable";
export { TimestampPrecision, Timestamp } from "./IonTimestamp";
export { toBase64 } from "./IonText";
export { decodeUtf8 } from "./IonUnicode";
import * as dom from "./dom";
export { dom };
export { load, loadAll } from "./dom";
export { IonEventType, IonEventFactory } from "./events/IonEvent";
export { IonEventStream } from "./events/IonEventStream";
export { EventStreamError } from "./events/EventStreamError";
export { ComparisonResult, ComparisonResultType } from "./ComparisonResult";
//# sourceMappingURL=Ion.js.map