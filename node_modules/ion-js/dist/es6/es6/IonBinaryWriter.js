/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { AbstractWriter } from "./AbstractWriter";
import { BigIntSerde } from "./BigIntSerde";
import { LowLevelBinaryWriter } from "./IonLowLevelBinaryWriter";
import { TimestampPrecision } from "./IonTimestamp";
import { IonTypes } from "./IonTypes";
import { encodeUtf8 } from "./IonUnicode";
import { Writeable } from "./IonWriteable";
import { _assertDefined, _sign } from "./util";
const MAJOR_VERSION = 1;
const MINOR_VERSION = 0;
const MAX_VALUE_LENGTH = 14;
const MAX_VALUE_LENGTH_FLAG = 14;
const NULL_VALUE_FLAG = 15;
const TYPE_DESCRIPTOR_LENGTH = 1;
const EMPTY_UINT8ARRAY = new Uint8Array();
var States;
(function (States) {
    States[States["VALUE"] = 0] = "VALUE";
    States[States["STRUCT_FIELD"] = 1] = "STRUCT_FIELD";
    States[States["STRUCT_VALUE"] = 2] = "STRUCT_VALUE";
    States[States["CLOSED"] = 3] = "CLOSED";
})(States || (States = {}));
var TypeCodes;
(function (TypeCodes) {
    TypeCodes[TypeCodes["NULL"] = 0] = "NULL";
    TypeCodes[TypeCodes["BOOL"] = 1] = "BOOL";
    TypeCodes[TypeCodes["POSITIVE_INT"] = 2] = "POSITIVE_INT";
    TypeCodes[TypeCodes["NEGATIVE_INT"] = 3] = "NEGATIVE_INT";
    TypeCodes[TypeCodes["FLOAT"] = 4] = "FLOAT";
    TypeCodes[TypeCodes["DECIMAL"] = 5] = "DECIMAL";
    TypeCodes[TypeCodes["TIMESTAMP"] = 6] = "TIMESTAMP";
    TypeCodes[TypeCodes["SYMBOL"] = 7] = "SYMBOL";
    TypeCodes[TypeCodes["STRING"] = 8] = "STRING";
    TypeCodes[TypeCodes["CLOB"] = 9] = "CLOB";
    TypeCodes[TypeCodes["BLOB"] = 10] = "BLOB";
    TypeCodes[TypeCodes["LIST"] = 11] = "LIST";
    TypeCodes[TypeCodes["SEXP"] = 12] = "SEXP";
    TypeCodes[TypeCodes["STRUCT"] = 13] = "STRUCT";
    TypeCodes[TypeCodes["ANNOTATION"] = 14] = "ANNOTATION";
})(TypeCodes || (TypeCodes = {}));
export class BinaryWriter extends AbstractWriter {
    constructor(symbolTable, writeable) {
        super();
        this.datagram = [];
        this.containers = [];
        this.state = States.VALUE;
        this.symbolTable = symbolTable;
        this.writer = new LowLevelBinaryWriter(writeable);
    }
    getBytes() {
        return this.writer.getBytes();
    }
    writeBlob(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.BLOB);
            return;
        }
        this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.BLOB, this.encodeAnnotations(this._annotations), value));
    }
    writeBoolean(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.BOOL);
            return;
        }
        this.addNode(new BooleanNode(this.writer, this.getCurrentContainer(), this.encodeAnnotations(this._annotations), value));
    }
    writeClob(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.CLOB);
            return;
        }
        this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.CLOB, this.encodeAnnotations(this._annotations), value));
    }
    writeDecimal(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.DECIMAL);
            return;
        }
        const exponent = value.getExponent();
        const coefficient = value.getCoefficient();
        const isPositiveZero = coefficient === 0n && !value.isNegative();
        if (isPositiveZero && exponent === 0 && _sign(exponent) === 1) {
            this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.DECIMAL, this.encodeAnnotations(this._annotations), new Uint8Array(0)));
            return;
        }
        const isNegative = value.isNegative();
        const writeCoefficient = isNegative || coefficient !== 0n;
        const coefficientBytes = writeCoefficient
            ? BigIntSerde.toSignedIntBytes(coefficient, isNegative)
            : EMPTY_UINT8ARRAY;
        const bufLen = LowLevelBinaryWriter.getVariableLengthSignedIntSize(exponent) +
            (writeCoefficient ? coefficientBytes.length : 0);
        const writer = new LowLevelBinaryWriter(new Writeable(bufLen));
        writer.writeVariableLengthSignedInt(exponent);
        if (writeCoefficient) {
            writer.writeBytes(coefficientBytes);
        }
        this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.DECIMAL, this.encodeAnnotations(this._annotations), writer.getBytes()));
    }
    writeFloat32(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.FLOAT);
            return;
        }
        let bytes;
        if (Object.is(value, 0)) {
            bytes = new Uint8Array(0);
        }
        else {
            const buffer = new ArrayBuffer(4);
            const dataview = new DataView(buffer);
            dataview.setFloat32(0, value, false);
            bytes = new Uint8Array(buffer);
        }
        this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.FLOAT, this.encodeAnnotations(this._annotations), bytes));
    }
    writeFloat64(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.FLOAT);
            return;
        }
        let bytes;
        if (Object.is(value, 0)) {
            bytes = new Uint8Array(0);
        }
        else {
            const buffer = new ArrayBuffer(8);
            const dataview = new DataView(buffer);
            dataview.setFloat64(0, value, false);
            bytes = new Uint8Array(buffer);
        }
        this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.FLOAT, this.encodeAnnotations(this._annotations), bytes));
    }
    writeInt(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.INT);
            return;
        }
        this.addNode(new IntNode(this.writer, this.getCurrentContainer(), this.encodeAnnotations(this._annotations), value));
    }
    writeNull(type) {
        if (type === undefined || type === null) {
            type = IonTypes.NULL;
        }
        this.checkWriteValue();
        this.addNode(new NullNode(this.writer, this.getCurrentContainer(), type, this.encodeAnnotations(this._annotations)));
    }
    writeString(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.STRING);
            return;
        }
        this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.STRING, this.encodeAnnotations(this._annotations), encodeUtf8(value)));
    }
    writeSymbol(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.SYMBOL);
        }
        else {
            const symbolId = this.symbolTable.addSymbol(value);
            const writer = new LowLevelBinaryWriter(new Writeable(LowLevelBinaryWriter.getUnsignedIntSize(symbolId)));
            writer.writeUnsignedInt(symbolId);
            this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.SYMBOL, this.encodeAnnotations(this._annotations), writer.getBytes()));
        }
    }
    writeTimestamp(value) {
        _assertDefined(value);
        this.checkWriteValue();
        if (value === null) {
            this.writeNull(IonTypes.TIMESTAMP);
            return;
        }
        const writer = new LowLevelBinaryWriter(new Writeable(12));
        writer.writeVariableLengthSignedInt(value.getLocalOffset());
        const date = value.getDate();
        writer.writeVariableLengthUnsignedInt(date.getUTCFullYear());
        if (value.getPrecision() >= TimestampPrecision.MONTH) {
            writer.writeVariableLengthUnsignedInt(date.getUTCMonth() + 1);
        }
        if (value.getPrecision() >= TimestampPrecision.DAY) {
            writer.writeVariableLengthUnsignedInt(date.getUTCDate());
        }
        if (value.getPrecision() >= TimestampPrecision.HOUR_AND_MINUTE) {
            writer.writeVariableLengthUnsignedInt(date.getUTCHours());
            writer.writeVariableLengthUnsignedInt(date.getUTCMinutes());
        }
        if (value.getPrecision() >= TimestampPrecision.SECONDS) {
            writer.writeVariableLengthUnsignedInt(value.getSecondsInt());
            const fractionalSeconds = value._getFractionalSeconds();
            if (fractionalSeconds.getExponent() !== 0) {
                writer.writeVariableLengthSignedInt(fractionalSeconds.getExponent());
                if (fractionalSeconds.getCoefficient() !== 0n) {
                    writer.writeBytes(BigIntSerde.toSignedIntBytes(fractionalSeconds.getCoefficient(), fractionalSeconds.isNegative()));
                }
            }
        }
        this.addNode(new BytesNode(this.writer, this.getCurrentContainer(), IonTypes.TIMESTAMP, this.encodeAnnotations(this._annotations), writer.getBytes()));
    }
    stepIn(type) {
        this.checkWriteValue();
        switch (type) {
            case IonTypes.LIST:
            case IonTypes.SEXP:
                this.addNode(new SequenceNode(this.writer, this.getCurrentContainer(), type, this.encodeAnnotations(this._annotations)));
                break;
            case IonTypes.STRUCT:
                this.addNode(new StructNode(this.writer, this.getCurrentContainer(), this.encodeAnnotations(this._annotations)));
                this.state = States.STRUCT_FIELD;
                break;
            default:
                throw new Error("Unrecognized container type");
        }
    }
    stepOut() {
        if (this.depth() === 0) {
            throw new Error("Not currently in a container");
        }
        if (this.state === States.STRUCT_VALUE) {
            throw new Error("Cannot exit a struct with a partially written field");
        }
        this.containers.pop();
        if (this.depth() > 0) {
            this.state =
                this.getCurrentContainer() instanceof StructNode
                    ? States.STRUCT_FIELD
                    : States.VALUE;
        }
        else {
            this.state = States.VALUE;
        }
    }
    _isInStruct() {
        return this.getCurrentContainer() instanceof StructNode;
    }
    writeFieldName(fieldName) {
        _assertDefined(fieldName);
        if (this.state !== States.STRUCT_FIELD) {
            throw new Error("Cannot write a field name outside of a struct");
        }
        this.fieldName = this.encodeAnnotations([fieldName]);
        this.state = States.STRUCT_VALUE;
    }
    depth() {
        return this.containers.length;
    }
    close() {
        this.checkClosed();
        if (this.depth() > 0) {
            throw new Error("Writer has one or more open containers; call stepOut() for each container prior to close()");
        }
        this.writeIvm();
        const datagram = this.datagram;
        this.datagram = [];
        this.writeSymbolTable();
        for (const node of datagram) {
            node.write();
        }
        this.state = States.CLOSED;
    }
    writeIvm() {
        this.writer.writeByte(0xe0);
        this.writer.writeByte(MAJOR_VERSION);
        this.writer.writeByte(MINOR_VERSION);
        this.writer.writeByte(0xea);
    }
    encodeAnnotations(annotations) {
        if (annotations.length === 0) {
            return new Uint8Array(0);
        }
        const writeable = new Writeable();
        const writer = new LowLevelBinaryWriter(writeable);
        for (const annotation of annotations) {
            const symbolId = this.symbolTable.addSymbol(annotation);
            writer.writeVariableLengthUnsignedInt(symbolId);
        }
        this._clearAnnotations();
        return writeable.getBytes();
    }
    getCurrentContainer() {
        return this.containers[this.containers.length - 1];
    }
    addNode(node) {
        if (this.depth() === 0) {
            this.datagram.push(node);
        }
        else {
            if (this.state === States.STRUCT_VALUE) {
                this.getCurrentContainer().addChild(node, this.fieldName);
                this.state = States.STRUCT_FIELD;
            }
            else {
                this.getCurrentContainer().addChild(node);
            }
        }
        if (node.isContainer()) {
            this.containers.push(node);
            this.state = States.VALUE;
        }
    }
    checkWriteValue() {
        this.checkClosed();
        if (this.state === States.STRUCT_FIELD) {
            throw new Error("Expected a struct field name instead of a value, call writeFieldName(string) with the desired name before calling stepIn(IonType) or writeIonType()");
        }
    }
    checkClosed() {
        if (this.state === States.CLOSED) {
            throw new Error("Writer is closed, no further operations are available");
        }
    }
    writeSymbolTable() {
        const hasImports = this.symbolTable.import.symbolTable.name != "$ion";
        const hasLocalSymbols = this.symbolTable.symbols.length > 0;
        if (!(hasImports || hasLocalSymbols)) {
            return;
        }
        this.setAnnotations(["$ion_symbol_table"]);
        this.stepIn(IonTypes.STRUCT);
        if (hasImports) {
            this.writeFieldName("imports");
            this.stepIn(IonTypes.LIST);
            this.writeImport(this.symbolTable.import);
            this.stepOut();
        }
        if (hasLocalSymbols) {
            this.writeFieldName("symbols");
            this.stepIn(IonTypes.LIST);
            for (const symbol_ of this.symbolTable.symbols) {
                if (symbol_ !== undefined) {
                    this.writeString(symbol_);
                }
            }
            this.stepOut();
        }
        this.stepOut();
        this.datagram[0].write();
    }
    writeImport(import_) {
        if (!import_) {
            return;
        }
        this.writeImport(import_.parent);
        this.stepIn(IonTypes.STRUCT);
        this.writeFieldName("name");
        this.writeString(import_.symbolTable.name);
        this.writeFieldName("version");
        this.writeInt(import_.symbolTable.version);
        this.writeFieldName("max_id");
        this.writeInt(import_.length);
        this.stepOut();
    }
}
export class AbstractNode {
    constructor(_writer, parent, _type, annotations) {
        this._writer = _writer;
        this.parent = parent;
        this._type = _type;
        this.annotations = annotations;
    }
    get typeCode() {
        return this._type.binaryTypeId;
    }
    get writer() {
        return this._writer;
    }
    static getLengthLength(length) {
        if (length < MAX_VALUE_LENGTH) {
            return 0;
        }
        else {
            return LowLevelBinaryWriter.getVariableLengthUnsignedIntSize(length);
        }
    }
    writeTypeDescriptorAndLength(typeCode, isNull, length) {
        let typeDescriptor = typeCode << 4;
        if (isNull) {
            typeDescriptor |= NULL_VALUE_FLAG;
            this.writer.writeByte(typeDescriptor);
        }
        else if (length < MAX_VALUE_LENGTH) {
            typeDescriptor |= length;
            this.writer.writeByte(typeDescriptor);
        }
        else {
            typeDescriptor |= MAX_VALUE_LENGTH_FLAG;
            this.writer.writeByte(typeDescriptor);
            this.writer.writeVariableLengthUnsignedInt(length);
        }
    }
    getContainedValueLength() {
        const valueLength = this.getValueLength();
        const valueLengthLength = AbstractNode.getLengthLength(valueLength);
        return TYPE_DESCRIPTOR_LENGTH + valueLengthLength + valueLength;
    }
    getAnnotatedContainerLength() {
        const annotationsLength = this.annotations.length;
        const annotationsLengthLength = LowLevelBinaryWriter.getVariableLengthUnsignedIntSize(annotationsLength);
        const containedValueLength = this.getContainedValueLength();
        return annotationsLength + annotationsLengthLength + containedValueLength;
    }
    getAnnotationsLength() {
        if (this.hasAnnotations()) {
            const annotationsLength = this.annotations.length;
            const annotationsLengthLength = LowLevelBinaryWriter.getVariableLengthUnsignedIntSize(annotationsLength);
            const containedValueLength = this.getContainedValueLength();
            const annotationsWrapperLengthLength = AbstractNode.getLengthLength(containedValueLength + annotationsLength + annotationsLengthLength);
            return (TYPE_DESCRIPTOR_LENGTH +
                annotationsWrapperLengthLength +
                annotationsLengthLength +
                annotationsLength);
        }
        return 0;
    }
    getLength() {
        const annotationsLength = this.getAnnotationsLength();
        const containedValueLength = this.getContainedValueLength();
        return annotationsLength + containedValueLength;
    }
    writeAnnotations() {
        if (!this.hasAnnotations()) {
            return;
        }
        const annotatedContainerLength = this.getAnnotatedContainerLength();
        this.writeTypeDescriptorAndLength(TypeCodes.ANNOTATION, false, annotatedContainerLength);
        this.writer.writeVariableLengthUnsignedInt(this.annotations.length);
        this.writer.writeBytes(new Uint8Array(this.annotations));
    }
    hasAnnotations() {
        return this.annotations.length > 0;
    }
}
class ContainerNode extends AbstractNode {
    constructor(writer, parent, type, annotations) {
        super(writer, parent, type, annotations);
    }
    isContainer() {
        return true;
    }
}
class SequenceNode extends ContainerNode {
    constructor(writer, parent, type, annotations) {
        super(writer, parent, type, annotations);
        this.children = [];
    }
    addChild(child, name) {
        this.children.push(child);
    }
    write() {
        this.writeAnnotations();
        this.writeTypeDescriptorAndLength(this.typeCode, false, this.getValueLength());
        for (const child of this.children) {
            child.write();
        }
    }
    getValueLength() {
        let valueLength = 0;
        for (const child of this.children) {
            valueLength += child.getLength();
        }
        return valueLength;
    }
    getLength() {
        if (this.length === undefined) {
            this.length = super.getLength();
        }
        return this.length;
    }
}
class StructNode extends ContainerNode {
    constructor(writer, parent, annotations) {
        super(writer, parent, IonTypes.STRUCT, annotations);
        this.fields = [];
    }
    addChild(child, fieldName) {
        if (fieldName === null || fieldName === undefined) {
            throw new Error("Cannot add a value to a struct without a field name");
        }
        this.fields.push({ name: fieldName, value: child });
    }
    getValueLength() {
        let valueLength = 0;
        for (const field of this.fields) {
            valueLength += field.name.length;
            valueLength += field.value.getLength();
        }
        return valueLength;
    }
    getLength() {
        if (this.length === undefined) {
            this.length = super.getLength();
        }
        return this.length;
    }
    write() {
        this.writeAnnotations();
        this.writeTypeDescriptorAndLength(this.typeCode, false, this.getValueLength());
        for (const field of this.fields) {
            this.writer.writeBytes(new Uint8Array(field.name));
            field.value.write();
        }
    }
}
export class LeafNode extends AbstractNode {
    addChild(child, name) {
        throw new Error("Cannot add a child to a leaf node");
    }
    isContainer() {
        return false;
    }
}
class BooleanNode extends LeafNode {
    constructor(writer, parent, annotations, value) {
        super(writer, parent, IonTypes.BOOL, annotations);
        this.value = value;
    }
    write() {
        this.writeAnnotations();
        this.writeTypeDescriptorAndLength(this.typeCode, false, this.value ? 1 : 0);
    }
    getValueLength() {
        return 0;
    }
}
class IntNode extends LeafNode {
    constructor(writer, parent, annotations, value) {
        super(writer, parent, IonTypes.INT, annotations);
        this.value = value;
        if (!(typeof this.value === "number" || typeof this.value === "bigint")) {
            throw new Error("Expected " + this.value + " to be a number or bigint");
        }
        if (this.value > 0n) {
            this.intTypeCode = TypeCodes.POSITIVE_INT;
            const writer = new LowLevelBinaryWriter(new Writeable(LowLevelBinaryWriter.getUnsignedIntSize(this.value)));
            writer.writeUnsignedInt(this.value);
            this.bytes = writer.getBytes();
        }
        else if (this.value < 0n) {
            this.intTypeCode = TypeCodes.NEGATIVE_INT;
            let magnitude;
            if (typeof value === "bigint") {
                if (value < 0n) {
                    magnitude = -value;
                }
                else {
                    magnitude = value;
                }
            }
            else {
                magnitude = Math.abs(value);
            }
            const writer = new LowLevelBinaryWriter(new Writeable(LowLevelBinaryWriter.getUnsignedIntSize(magnitude)));
            writer.writeUnsignedInt(magnitude);
            this.bytes = writer.getBytes();
        }
        else {
            this.intTypeCode = TypeCodes.POSITIVE_INT;
            this.bytes = new Uint8Array(0);
        }
    }
    write() {
        this.writeAnnotations();
        this.writeTypeDescriptorAndLength(this.intTypeCode, false, this.bytes.length);
        this.writer.writeBytes(this.bytes);
    }
    getValueLength() {
        return this.bytes.length;
    }
}
class BytesNode extends LeafNode {
    constructor(writer, parent, type, annotations, value) {
        super(writer, parent, type, annotations);
        this.value = value;
    }
    write() {
        this.writeAnnotations();
        this.writeTypeDescriptorAndLength(this.typeCode, false, this.value.length);
        this.writer.writeBytes(this.value);
    }
    getValueLength() {
        return this.value.length;
    }
}
export class NullNode extends LeafNode {
    constructor(writer, parent, type, annotations) {
        super(writer, parent, type, annotations);
    }
    write() {
        this.writeAnnotations();
        this.writeTypeDescriptorAndLength(this.typeCode, true, 0);
    }
    getValueLength() {
        return 0;
    }
}
//# sourceMappingURL=IonBinaryWriter.js.map