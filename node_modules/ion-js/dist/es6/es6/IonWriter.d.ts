/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { Decimal } from "./IonDecimal";
import { Reader } from "./IonReader";
import { Timestamp } from "./IonTimestamp";
import { IonType } from "./IonType";
export interface Writer {
    writeNull(type: IonType): void;
    writeBoolean(value: boolean | null): void;
    writeInt(value: number | bigint | null): void;
    writeFloat32(value: number | null): void;
    writeFloat64(value: number | null): void;
    writeDecimal(value: Decimal | null): void;
    writeTimestamp(value: Timestamp | null): void;
    writeString(value: string | null): void;
    writeSymbol(value: string | null): void;
    writeBlob(value: Uint8Array | null): void;
    writeClob(value: Uint8Array | null): void;
    writeValue(reader: Reader): void;
    writeValues(reader: Reader): void;
    writeFieldName(fieldName: string): void;
    stepIn(type: IonType): void;
    stepOut(): void;
    addAnnotation(annotation: string): void;
    setAnnotations(annotations: string[]): void;
    close(): void;
    getBytes(): Uint8Array;
    depth(): number;
}
