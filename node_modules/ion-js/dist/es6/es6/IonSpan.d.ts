/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
export declare abstract class Span {
    protected readonly _type: number;
    constructor(_type: number);
    static error(): void;
    abstract position(): number;
    abstract next(): number;
    abstract valueAt(index: number): number;
    abstract getRemaining(): number;
    abstract setRemaining(r: number): void;
    abstract is_empty(): boolean;
    abstract skip(dist: number): void;
    abstract unread(ch: number): void;
    abstract peek(): number;
    abstract chunk(length: number): any;
    write(b: number): never;
    protected abstract clone(start: number, len: number): Span;
}
export declare class StringSpan extends Span {
    private _src;
    private _pos;
    private _start;
    private _limit;
    private _line;
    private _old_line_start;
    private _line_start;
    constructor(src: string);
    viewSource(): string;
    position(): number;
    getRemaining(): number;
    setRemaining(r: number): void;
    is_empty(): boolean;
    next(): number;
    _inc_line(): void;
    unread(ch: number): void;
    peek(): number;
    skip(dist: number): void;
    valueAt(ii: number): number;
    chunk(length: number): string;
    getCodePoint(index: number): number;
    line_number(): number;
    offset(): number;
    clone(start: number): StringSpan;
}
export declare class BinarySpan extends Span {
    private _src;
    private _pos;
    private _start;
    private _limit;
    constructor(src: Uint8Array);
    position(): number;
    getRemaining(): number;
    setRemaining(r: number): void;
    is_empty(): boolean;
    next(): number;
    view(length: number): Uint8Array;
    chunk(length: number): Uint8Array;
    unread(b: number): void;
    peek(): number;
    skip(dist: number): void;
    valueAt(ii: number): number;
    clone(start: number, len: number): BinarySpan;
}
