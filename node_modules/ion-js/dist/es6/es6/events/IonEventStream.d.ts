/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { ComparisonResult } from "../ComparisonResult";
import { Reader } from "../IonReader";
import { Writer } from "../IonWriter";
import { IonEvent } from "./IonEvent";
export declare class IonEventStream {
    events: IonEvent[];
    private reader;
    private eventFactory;
    isEventStream: boolean;
    constructor(reader: Reader);
    writeEventStream(writer: Writer): void;
    writeIon(writer: Writer): void;
    getEvents(): IonEvent[];
    equals(expected: IonEventStream): boolean;
    compare(expected: IonEventStream): ComparisonResult;
    isEmbedded(event: IonEvent): boolean;
    private generateStream;
    private endContainer;
    private marshalStream;
    private marshalEvent;
    private parseIonType;
    private parseAnnotations;
    private parseBinaryValue;
    private parseImports;
    private resolveFieldNameFromSerializedSymbolToken;
}
