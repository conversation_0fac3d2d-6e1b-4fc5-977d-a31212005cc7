/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
export default class SignAndMagnitudeInt {
    readonly _magnitude: bigint;
    readonly _isNegative: boolean;
    constructor(_magnitude: bigint, _isNegative?: boolean);
    get magnitude(): bigint;
    get isNegative(): boolean;
    static fromNumber(value: number): SignAndMagnitudeInt;
    equals(other: SignAndMagnitudeInt): boolean;
}
