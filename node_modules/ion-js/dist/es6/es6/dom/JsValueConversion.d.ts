import { IonType } from "../IonType";
import { Value } from "./Value";
export declare const _NativeJsBoolean: BooleanConstructor;
export declare const _NativeJsString: StringConstructor;
export interface FromJsValue {
    _fromJsValue(jsValue: any, annotations: string[]): Value;
}
export declare function _domConstructorFor(ionType: IonType): FromJsValue;
export declare function _ionValueFromJsValue(value: any, annotations?: string[]): Value;
