import * as ion from "../Ion";
import { Decimal, IonTypes } from "../Ion";
import { FromJsConstructorBuilder, } from "./FromJsConstructor";
import { Value } from "./Value";
const _fromJsConstructor = new FromJsConstructorBuilder()
    .withClasses(Date, ion.Timestamp)
    .build();
export class Timestamp extends Value(Date, IonTypes.TIMESTAMP, _fromJsConstructor) {
    constructor(dateOrTimestamp, annotations = []) {
        let date;
        let timestamp;
        if (dateOrTimestamp instanceof Date) {
            date = dateOrTimestamp;
            timestamp = Timestamp._timestampFromDate(date);
        }
        else {
            timestamp = dateOrTimestamp;
            date = timestamp.getDate();
        }
        super(date);
        this._date = date;
        this._timestamp = timestamp;
        this._setAnnotations(annotations);
    }
    static _timestampFromDate(date) {
        const milliseconds = date.getUTCSeconds() * 1000 + date.getUTCMilliseconds();
        const fractionalSeconds = new Decimal(milliseconds, -3);
        return new ion.Timestamp(0, date.getUTCFullYear(), date.getUTCMonth() + 1, date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes(), fractionalSeconds);
    }
    timestampValue() {
        return this._timestamp;
    }
    dateValue() {
        return this._date;
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeTimestamp(this.timestampValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Timestamp) {
            isSupportedType = true;
            valueToCompare = other.timestampValue();
        }
        else if (!options.onlyCompareIon) {
            if (other instanceof ion.Timestamp) {
                isSupportedType = true;
                valueToCompare = other;
            }
            else if (other instanceof Date) {
                if (this.dateValue().getTime() === other.getTime()) {
                    return true;
                }
                else {
                    return false;
                }
            }
        }
        if (!isSupportedType) {
            return false;
        }
        if (options.ignoreTimestampPrecision) {
            return this.timestampValue().compareTo(valueToCompare) === 0;
        }
        return this.timestampValue().equals(valueToCompare);
    }
}
//# sourceMappingURL=Timestamp.js.map