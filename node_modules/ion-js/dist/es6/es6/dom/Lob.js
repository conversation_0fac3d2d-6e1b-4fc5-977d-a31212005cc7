import { IonTypes } from "../Ion";
import { FromJsConstructorBuilder, } from "./FromJsConstructor";
import { Value } from "./Value";
const _fromJsConstructor = new FromJsConstructorBuilder()
    .withClasses(Uint8Array)
    .build();
export function Lob(ionType) {
    return class extends Value(Uint8Array, ionType, _fromJsConstructor) {
        constructor(data, annotations = []) {
            super(data);
            this._setAnnotations(annotations);
        }
        uInt8ArrayValue() {
            return this;
        }
        _valueEquals(other, options = {
            epsilon: null,
            ignoreAnnotations: false,
            ignoreTimestampPrecision: false,
            onlyCompareIon: true,
        }) {
            let isSupportedType = false;
            let valueToCompare = null;
            if (options.onlyCompareIon) {
                if (other.getType() === IonTypes.CLOB ||
                    other.getType() === IonTypes.BLOB) {
                    isSupportedType = true;
                    valueToCompare = other.uInt8ArrayValue();
                }
            }
            else {
                if (other instanceof Uint8Array) {
                    isSupportedType = true;
                    valueToCompare = other.valueOf();
                }
            }
            if (!isSupportedType) {
                return false;
            }
            let current = this.uInt8ArrayValue();
            let expected = valueToCompare;
            if (current.length !== expected.length) {
                return false;
            }
            for (let i = 0; i < current.length; i++) {
                if (current[i] !== expected[i]) {
                    return false;
                }
            }
            return true;
        }
    };
}
//# sourceMappingURL=Lob.js.map