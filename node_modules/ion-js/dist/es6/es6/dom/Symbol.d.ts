import { Writer } from "../Ion";
import { Value } from "./Value";
declare const Symbol_base: {
    new (...args: any[]): {
        _ionType: import("../IonType").IonType;
        _ionAnnotations: string[];
        _unsupportedOperation<T extends Value>(functionName: string): never;
        getType(): import("../IonType").IonType;
        _setAnnotations(annotations: string[]): void;
        getAnnotations(): string[];
        isNull(): boolean;
        booleanValue(): boolean | null;
        numberValue(): number | null;
        bigIntValue(): bigint | null;
        decimalValue(): import("../IonDecimal").Decimal | null;
        stringValue(): string | null;
        dateValue(): Date | null;
        timestampValue(): import("../IonTimestamp").Timestamp | null;
        uInt8ArrayValue(): Uint8Array | null;
        fieldNames(): string[];
        fields(): [string, Value][];
        allFields(): [string, Value[]][];
        elements(): Value[];
        get(...pathElements: (string | number)[]): Value | null;
        getAll(...pathElements: (string | number)[]): Value[] | null;
        as<T_1 extends Value>(ionValueType: import("./Value").Constructor<T_1>): T_1;
        writeTo(writer: Writer): void;
        deleteField(name: string): boolean;
        _valueEquals(other: any, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
            onlyCompareIon?: boolean | undefined;
            coerceNumericType: boolean;
        }): boolean;
        equals(other: any, options?: {
            epsilon?: number | null | undefined;
        }): boolean;
        ionEquals(other: Value, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
        }): boolean;
    };
    _getIonType(): import("../IonType").IonType;
    _fromJsValue(jsValue: any, annotations: string[]): Value;
} & StringConstructor;
export declare class Symbol extends Symbol_base {
    constructor(symbolText: string, annotations?: string[]);
    stringValue(): string;
    writeTo(writer: Writer): void;
    _valueEquals(other: any, options?: {
        epsilon?: number | null;
        ignoreAnnotations?: boolean;
        ignoreTimestampPrecision?: boolean;
        onlyCompareIon?: boolean;
    }): boolean;
    compareValue(expectedValue: string): number;
}
export {};
