import * as ion from "../Ion";
import { IntSize, IonTypes, makeReader } from "../Ion";
import { BinaryReader } from "../IonBinaryReader";
import { TextReader } from "../IonTextReader";
import { Blob } from "./Blob";
import { Clob } from "./Clob";
import { Decimal } from "./Decimal";
import { Float } from "./Float";
import { Integer } from "./Integer";
import { List } from "./List";
import { Null } from "./Null";
import { SExpression } from "./SExpression";
import { Struct } from "./Struct";
import { Symbol } from "./Symbol";
import { Timestamp } from "./Timestamp";
export function loadAll(ionData) {
    const reader = _createReader(ionData);
    const ionValues = [];
    while (reader.next()) {
        ionValues.push(_loadValue(reader));
    }
    return ionValues;
}
export function load(ionData) {
    const reader = _createReader(ionData);
    if (reader.type() === null) {
        reader.next();
    }
    return reader.type() === null ? null : _loadValue(reader);
}
function _createReader(ionData) {
    if (ionData instanceof TextReader || ionData instanceof BinaryReader) {
        return ionData;
    }
    return makeReader(ionData);
}
function _loadValue(reader) {
    const ionType = reader.type();
    if (ionType === null) {
        throw new Error("loadValue() called when no further values were available to read.");
    }
    const annotations = reader.annotations();
    if (reader.isNull()) {
        return new Null(reader.type(), annotations);
    }
    switch (ionType) {
        case IonTypes.NULL:
            return new Null(IonTypes.NULL, annotations);
        case IonTypes.BOOL:
            return new ion.dom.Boolean(reader.booleanValue(), annotations);
        case IonTypes.INT:
            return reader.intSize() == IntSize.Number
                ? new Integer(reader.numberValue(), annotations)
                : new Integer(reader.bigIntValue(), annotations);
        case IonTypes.FLOAT:
            return new Float(reader.numberValue(), annotations);
        case IonTypes.DECIMAL:
            return new Decimal(reader.decimalValue(), annotations);
        case IonTypes.TIMESTAMP:
            return new Timestamp(reader.timestampValue(), annotations);
        case IonTypes.SYMBOL:
            return new Symbol(reader.stringValue(), annotations);
        case IonTypes.STRING:
            return new ion.dom.String(reader.stringValue(), annotations);
        case IonTypes.CLOB:
            return new Clob(reader.uInt8ArrayValue(), annotations);
        case IonTypes.BLOB:
            return new Blob(reader.uInt8ArrayValue(), annotations);
        case IonTypes.LIST:
            return _loadList(reader);
        case IonTypes.SEXP:
            return _loadSExpression(reader);
        case IonTypes.STRUCT:
            return _loadStruct(reader);
        default:
            throw new Error(`Unrecognized IonType '${ionType}' found.`);
    }
}
function _loadStruct(reader) {
    const children = new Map();
    const annotations = reader.annotations();
    reader.stepIn();
    while (reader.next()) {
        if (children.has(reader.fieldName())) {
            children.get(reader.fieldName()).push(_loadValue(reader));
        }
        else {
            children.set(reader.fieldName(), [_loadValue(reader)]);
        }
    }
    reader.stepOut();
    return new Struct(children.entries(), annotations);
}
function _loadList(reader) {
    const annotations = reader.annotations();
    return new List(_loadSequence(reader), annotations);
}
function _loadSExpression(reader) {
    const annotations = reader.annotations();
    return new SExpression(_loadSequence(reader), annotations);
}
function _loadSequence(reader) {
    const children = [];
    reader.stepIn();
    while (reader.next()) {
        children.push(_loadValue(reader));
    }
    reader.stepOut();
    return children;
}
export { Value } from "./Value";
export { Null } from "./Null";
export { Boolean } from "./Boolean";
export { Integer } from "./Integer";
export { Float } from "./Float";
export { Decimal } from "./Decimal";
export { Timestamp } from "./Timestamp";
export { String } from "./String";
export { Symbol } from "./Symbol";
export { Blob } from "./Blob";
export { Clob } from "./Clob";
export { Struct } from "./Struct";
export { List } from "./List";
export { SExpression } from "./SExpression";
//# sourceMappingURL=index.js.map