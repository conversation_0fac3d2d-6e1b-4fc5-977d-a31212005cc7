import { IonTypes } from "../Ion";
import { FromJsConstructorBuilder, Primitives, } from "./FromJsConstructor";
import { Value } from "./Value";
import { Decimal } from "./Decimal";
import * as ion from "../Ion";
const _fromJsConstructor = new FromJsConstructorBuilder()
    .withPrimitives(Primitives.Number)
    .withClassesToUnbox(Number)
    .build();
export class Float extends Value(Number, IonTypes.FLOAT, _fromJsConstructor) {
    constructor(value, annotations = []) {
        super(value);
        this._setAnnotations(annotations);
    }
    numberValue() {
        return +this.valueOf();
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeFloat64(this.numberValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
        coerceNumericType: false,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Float) {
            isSupportedType = true;
            valueToCompare = other.numberValue();
        }
        else if (options.coerceNumericType === true && other instanceof Decimal) {
            let thisValue = new ion.Decimal(other.toString());
            return thisValue.equals(other.decimalValue());
        }
        else if (!options.onlyCompareIon) {
            if (other instanceof Number || typeof other === "number") {
                isSupportedType = true;
                valueToCompare = other.valueOf();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        let result = Object.is(this.numberValue(), valueToCompare);
        if (options.epsilon != null) {
            if (result ||
                Math.abs(this.numberValue() - valueToCompare) <= options.epsilon) {
                return true;
            }
        }
        return result;
    }
}
//# sourceMappingURL=Float.js.map