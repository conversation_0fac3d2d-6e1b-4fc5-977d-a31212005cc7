import { IonTypes } from "../Ion";
import { FromJsConstructorBuilder, Primitives, } from "./FromJsConstructor";
import { Value } from "./Value";
const _fromJsConstructor = new FromJsConstructorBuilder()
    .withPrimitives(Primitives.String)
    .withClassesToUnbox(String)
    .build();
export class Symbol extends Value(String, IonTypes.SYMBOL, _fromJsConstructor) {
    constructor(symbolText, annotations = []) {
        super(symbolText);
        this._setAnnotations(annotations);
    }
    stringValue() {
        return this.toString();
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeSymbol(this.stringValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Symbol) {
            isSupportedType = true;
            valueToCompare = other.stringValue();
        }
        else if (!options.onlyCompareIon) {
            if (typeof other === "string" || other instanceof String) {
                isSupportedType = true;
                valueToCompare = other.valueOf();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        return this.compareValue(valueToCompare) === 0;
    }
    compareValue(expectedValue) {
        return this.stringValue().localeCompare(expectedValue);
    }
}
//# sourceMappingURL=Symbol.js.map