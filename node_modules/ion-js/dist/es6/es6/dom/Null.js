import { IonTypes } from "../Ion";
import { FromJsConstructor } from "./FromJsConstructor";
import { Value } from "./Value";
export class Null extends Value(Object, IonTypes.NULL, FromJsConstructor.NONE) {
    constructor(ionType = IonTypes.NULL, annotations = []) {
        super();
        this._ionType = ionType;
        this._setAnnotations(annotations);
    }
    static _operationIsSupported(ionType, operation) {
        return Null._supportedIonTypesByOperation.get(operation).has(ionType);
    }
    isNull() {
        return true;
    }
    _convertToJsNull(operation) {
        if (Null._operationIsSupported(this.getType(), operation)) {
            return null;
        }
        throw new Error(`${operation}() is not supported by Ion type ${this.getType().name}`);
    }
    _unsupportedOperationOrNullDereference(operation) {
        if (Null._operationIsSupported(this.getType(), operation)) {
            throw new Error(`${operation}() called on a null ${this.getType().name}.`);
        }
        throw new Error(`${operation}() is not supported by Ion type ${this.getType().name}`);
    }
    booleanValue() {
        return this._convertToJsNull("booleanValue");
    }
    numberValue() {
        return this._convertToJsNull("numberValue");
    }
    bigIntValue() {
        return this._convertToJsNull("bigIntValue");
    }
    decimalValue() {
        return this._convertToJsNull("decimalValue");
    }
    stringValue() {
        return this._convertToJsNull("stringValue");
    }
    dateValue() {
        return this._convertToJsNull("dateValue");
    }
    uInt8ArrayValue() {
        return this._convertToJsNull("uInt8ArrayValue");
    }
    fieldNames() {
        this._unsupportedOperationOrNullDereference("fieldNames");
    }
    fields() {
        this._unsupportedOperationOrNullDereference("fields");
    }
    elements() {
        this._unsupportedOperationOrNullDereference("elements");
    }
    get(...pathElements) {
        return null;
    }
    toString() {
        if (this.getType() == IonTypes.NULL) {
            return "null";
        }
        return "null." + this._ionType.name;
    }
    toJSON() {
        return null;
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeNull(this.getType());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Null) {
            isSupportedType = true;
            valueToCompare = other;
        }
        else if (!options.onlyCompareIon) {
            if (other === null && this._ionType.name === "null") {
                return true;
            }
        }
        if (!isSupportedType) {
            return false;
        }
        return this._ionType.name === valueToCompare._ionType.name;
    }
}
Null._supportedIonTypesByOperation = new Map([
    ["booleanValue", new Set([IonTypes.BOOL])],
    ["numberValue", new Set([IonTypes.INT, IonTypes.FLOAT, IonTypes.DECIMAL])],
    ["bigIntValue", new Set([IonTypes.INT])],
    ["decimalValue", new Set([IonTypes.DECIMAL])],
    ["stringValue", new Set([IonTypes.STRING, IonTypes.SYMBOL])],
    ["dateValue", new Set([IonTypes.TIMESTAMP])],
    ["timestampValue", new Set([IonTypes.TIMESTAMP])],
    ["uInt8ArrayValue", new Set([IonTypes.BLOB, IonTypes.CLOB])],
    ["fields", new Set([IonTypes.STRUCT])],
    ["fieldNames", new Set([IonTypes.STRUCT])],
    ["elements", new Set([IonTypes.LIST, IonTypes.SEXP, IonTypes.STRUCT])],
]);
//# sourceMappingURL=Null.js.map