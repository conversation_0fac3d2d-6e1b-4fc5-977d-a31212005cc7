import { Decimal, dom, Timestamp } from "../Ion";
import { IonTypes } from "../IonTypes";
import { _hasValue } from "../util";
export const _NativeJsBoolean = Boolean;
export const _NativeJsString = String;
let _domTypesByIonType = null;
function _getDomTypesByIonTypeMap() {
    if (_domTypesByIonType === null) {
        _domTypesByIonType = new Map([
            [IonTypes.NULL, dom.Null],
            [IonTypes.BOOL, dom.Boolean],
            [IonTypes.INT, dom.Integer],
            [IonTypes.FLOAT, dom.Float],
            [IonTypes.DECIMAL, dom.Decimal],
            [IonTypes.TIMESTAMP, dom.Timestamp],
            [IonTypes.STRING, dom.String],
            [IonTypes.BLOB, dom.Blob],
            [IonTypes.LIST, dom.List],
            [IonTypes.STRUCT, dom.Struct],
        ]);
    }
    return _domTypesByIonType;
}
export function _domConstructorFor(ionType) {
    const domConstructor = _getDomTypesByIonTypeMap().get(ionType);
    if (!_hasValue(domConstructor)) {
        throw new Error(`No dom type constructor was found for Ion type ${ionType.name}`);
    }
    return domConstructor;
}
function _inferType(value) {
    if (value === undefined) {
        throw new Error("Cannot create an Ion value from `undefined`.");
    }
    if (value === null) {
        return IonTypes.NULL;
    }
    const valueType = typeof value;
    switch (valueType) {
        case "string":
            return IonTypes.STRING;
        case "number":
            return Number.isInteger(value) ? IonTypes.INT : IonTypes.FLOAT;
        case "boolean":
            return IonTypes.BOOL;
        case "object":
            break;
        case "bigint":
            return IonTypes.INT;
        default:
            throw new Error(`Value.from() does not support the JS primitive type ${valueType}.`);
    }
    if (value instanceof BigInt) {
        return IonTypes.INT;
    }
    if (value instanceof Number) {
        return Number.isInteger(value.valueOf()) ? IonTypes.INT : IonTypes.FLOAT;
    }
    if (value instanceof Boolean) {
        return IonTypes.BOOL;
    }
    if (value instanceof String) {
        return IonTypes.STRING;
    }
    if (value instanceof Decimal) {
        return IonTypes.DECIMAL;
    }
    if (value instanceof Date || value instanceof Timestamp) {
        return IonTypes.TIMESTAMP;
    }
    if (value instanceof Uint8Array) {
        return IonTypes.BLOB;
    }
    if (value instanceof Array) {
        return IonTypes.LIST;
    }
    return IonTypes.STRUCT;
}
export function _ionValueFromJsValue(value, annotations = []) {
    const ionType = _inferType(value);
    const ionTypeConstructor = _domConstructorFor(ionType);
    return ionTypeConstructor._fromJsValue(value, annotations);
}
//# sourceMappingURL=JsValueConversion.js.map