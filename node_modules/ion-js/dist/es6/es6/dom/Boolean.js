import { IonTypes } from "../Ion";
import { FromJsConstructorBuilder, Primitives, } from "./FromJsConstructor";
import { _NativeJsBoolean } from "./JsValueConversion";
import { Value } from "./Value";
const _fromJsConstructor = new FromJsConstructorBuilder()
    .withPrimitives(Primitives.Boolean)
    .withClassesToUnbox(_NativeJsBoolean)
    .build();
export class Boolean extends Value(_NativeJsBoolean, IonTypes.BOOL, _fromJsConstructor) {
    constructor(value, annotations = []) {
        super(value);
        this._setAnnotations(annotations);
    }
    booleanValue() {
        return this.valueOf();
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeBoolean(this.booleanValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Boolean) {
            isSupportedType = true;
            valueToCompare = other.booleanValue();
        }
        else if (!options.onlyCompareIon) {
            if (typeof other === "boolean" || other instanceof _NativeJsBoolean) {
                isSupportedType = true;
                valueToCompare = other.valueOf();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        if (this.booleanValue() !== valueToCompare) {
            return false;
        }
        return true;
    }
}
//# sourceMappingURL=Boolean.js.map