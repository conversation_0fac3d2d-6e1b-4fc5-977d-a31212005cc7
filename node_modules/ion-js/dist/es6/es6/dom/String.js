import { IonTypes } from "../Ion";
import { FromJsConstructorBuilder, Primitives, } from "./FromJsConstructor";
import { _NativeJsString } from "./JsValueConversion";
import { Value } from "./Value";
const _fromJsConstructor = new FromJsConstructorBuilder()
    .withPrimitives(Primitives.String)
    .withClassesToUnbox(_NativeJsString)
    .build();
export class String extends Value(_NativeJsString, IonTypes.STRING, _fromJsConstructor) {
    constructor(text, annotations = []) {
        super(text);
        this._setAnnotations(annotations);
    }
    stringValue() {
        return this.toString();
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeString(this.stringValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof String) {
            isSupportedType = true;
            valueToCompare = other.stringValue();
        }
        else if (!options.onlyCompareIon) {
            if (typeof other === "string" || other instanceof _NativeJsString) {
                isSupportedType = true;
                valueToCompare = other.valueOf();
            }
        }
        if (!isSupportedType) {
            return false;
        }
        return this.compareValue(valueToCompare) === 0;
    }
    compareValue(expectedValue) {
        return this.stringValue().localeCompare(expectedValue);
    }
}
//# sourceMappingURL=String.js.map