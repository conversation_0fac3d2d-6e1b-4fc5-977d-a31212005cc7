import { _hasValue } from "../util";
import * as JsValueConversion from "./JsValueConversion";
const _DOM_VALUE_SIGNET = Symbol("ion.dom.Value");
export function Value(BaseClass, ionType, fromJsConstructor) {
    const newClass = class extends BaseClass {
        constructor(...args) {
            super(...args);
            this._ionType = ionType;
            this._ionAnnotations = [];
            Object.defineProperty(this, "_ionType", { enumerable: false });
            Object.defineProperty(this, "_ionAnnotations", { enumerable: false });
        }
        _unsupportedOperation(functionName) {
            throw new Error(`Value#${functionName}() is not supported by Ion type ${this.getType().name}`);
        }
        getType() {
            return this._ionType;
        }
        _setAnnotations(annotations) {
            this._ionAnnotations = annotations;
        }
        getAnnotations() {
            if (this._ionAnnotations === null) {
                return [];
            }
            return this._ionAnnotations;
        }
        isNull() {
            return false;
        }
        booleanValue() {
            this._unsupportedOperation("booleanValue");
        }
        numberValue() {
            this._unsupportedOperation("numberValue");
        }
        bigIntValue() {
            this._unsupportedOperation("bigIntValue");
        }
        decimalValue() {
            this._unsupportedOperation("decimalValue");
        }
        stringValue() {
            this._unsupportedOperation("stringValue");
        }
        dateValue() {
            this._unsupportedOperation("dateValue");
        }
        timestampValue() {
            this._unsupportedOperation("timestampValue");
        }
        uInt8ArrayValue() {
            this._unsupportedOperation("uInt8ArrayValue");
        }
        fieldNames() {
            this._unsupportedOperation("fieldNames");
        }
        fields() {
            this._unsupportedOperation("fields");
        }
        allFields() {
            this._unsupportedOperation("allFields");
        }
        elements() {
            this._unsupportedOperation("elements");
        }
        get(...pathElements) {
            this._unsupportedOperation("get");
        }
        getAll(...pathElements) {
            this._unsupportedOperation("getAll");
        }
        as(ionValueType) {
            if (this instanceof ionValueType) {
                return this;
            }
            throw new Error(`${this.constructor.name} is not an instance of ${ionValueType.name}`);
        }
        writeTo(writer) {
            this._unsupportedOperation("writeTo");
        }
        deleteField(name) {
            this._unsupportedOperation("deleteField");
        }
        _valueEquals(other, options = {
            epsilon: null,
            ignoreAnnotations: false,
            ignoreTimestampPrecision: false,
            onlyCompareIon: true,
            coerceNumericType: false,
        }) {
            this._unsupportedOperation("_valueEquals");
        }
        equals(other, options = { epsilon: null }) {
            let onlyCompareIon = false;
            if (other instanceof Value) {
                onlyCompareIon = true;
            }
            return this._valueEquals(other, {
                onlyCompareIon: onlyCompareIon,
                ignoreTimestampPrecision: true,
                ignoreAnnotations: true,
                epsilon: options.epsilon,
                coerceNumericType: true,
            });
        }
        ionEquals(other, options = {
            epsilon: null,
            ignoreAnnotations: false,
            ignoreTimestampPrecision: false,
        }) {
            if (!options.ignoreAnnotations) {
                if (!(other instanceof Value)) {
                    return false;
                }
                let actualAnnotations = this.getAnnotations();
                let expectedAnnotations = other.getAnnotations();
                if (actualAnnotations.length !== expectedAnnotations.length) {
                    return false;
                }
                for (let i = 0; i < actualAnnotations.length; i++) {
                    if (actualAnnotations[i].localeCompare(expectedAnnotations[i]) !== 0) {
                        return false;
                    }
                }
            }
            let ion_options = {
                onlyCompareIon: true,
                ignoreTimestampPrecision: options.ignoreTimestampPrecision,
                epsilon: options.epsilon,
                coerceNumericType: false,
            };
            return this._valueEquals(other, ion_options);
        }
        static _getIonType() {
            return ionType;
        }
        static _fromJsValue(jsValue, annotations) {
            return fromJsConstructor.construct(this, jsValue, annotations);
        }
    };
    Object.defineProperty(newClass, _DOM_VALUE_SIGNET, {
        writable: false,
        enumerable: false,
        value: _DOM_VALUE_SIGNET,
    });
    return newClass;
}
(function (Value) {
    function from(value, annotations) {
        if (value instanceof Value) {
            if (_hasValue(annotations)) {
                throw new Error("Value.from() does not support overriding the annotations on a dom.Value" +
                    " passed as an argument.");
            }
            return value;
        }
        return JsValueConversion._ionValueFromJsValue(value, annotations);
    }
    Value.from = from;
})(Value || (Value = {}));
Object.defineProperty(Value, Symbol.hasInstance, {
    get: () => (instance) => {
        return (_hasValue(instance) &&
            _hasValue(instance.constructor) &&
            _DOM_VALUE_SIGNET in instance.constructor &&
            instance.constructor[_DOM_VALUE_SIGNET] === _DOM_VALUE_SIGNET);
    },
});
//# sourceMappingURL=Value.js.map