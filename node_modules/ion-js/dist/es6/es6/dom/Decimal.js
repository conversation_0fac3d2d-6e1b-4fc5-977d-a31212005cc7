import * as ion from "../Ion";
import { IonTypes } from "../Ion";
import { FromJsConstructorBuilder, } from "./FromJsConstructor";
import { Value } from "./Value";
import { Float } from "./Float";
const _fromJsConstructor = new FromJsConstructorBuilder()
    .withClasses(ion.Decimal)
    .build();
export class Decimal extends Value(Number, IonTypes.DECIMAL, _fromJsConstructor) {
    constructor(value, annotations = []) {
        if (typeof value === "string") {
            let numberValue = Number(value);
            super(numberValue);
            this._decimalValue = new ion.Decimal(value);
            this._numberValue = numberValue;
        }
        else if (value instanceof ion.Decimal) {
            super(value.numberValue());
            this._decimalValue = value;
            this._numberValue = value.numberValue();
        }
        else if (typeof value === "number") {
            super(value);
            this._decimalValue = new ion.Decimal("" + value);
            this._numberValue = value;
        }
        else {
            throw new Error("Decimal value can only be created from number, ion.Decimal or string");
        }
        this._setAnnotations(annotations);
    }
    numberValue() {
        return this._numberValue;
    }
    decimalValue() {
        return this._decimalValue;
    }
    toString() {
        return this._decimalValue.toString();
    }
    valueOf() {
        return this._numberValue;
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeDecimal(this.decimalValue());
    }
    _valueEquals(other, options = {
        epsilon: null,
        ignoreAnnotations: false,
        ignoreTimestampPrecision: false,
        onlyCompareIon: true,
        coerceNumericType: false,
    }) {
        let isSupportedType = false;
        let valueToCompare = null;
        if (other instanceof Decimal) {
            isSupportedType = true;
            valueToCompare = other.decimalValue();
        }
        else if (options.coerceNumericType === true && other instanceof Float) {
            isSupportedType = true;
            valueToCompare = new ion.Decimal(other.toString());
        }
        else if (!options.onlyCompareIon) {
            if (other instanceof ion.Decimal) {
                isSupportedType = true;
                valueToCompare = other;
            }
            else if (other instanceof Number || typeof other === "number") {
                isSupportedType = true;
                valueToCompare = new ion.Decimal(other.toString());
            }
        }
        if (!isSupportedType) {
            return false;
        }
        return this.decimalValue().equals(valueToCompare);
    }
}
//# sourceMappingURL=Decimal.js.map