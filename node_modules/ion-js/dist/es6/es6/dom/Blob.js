import { IonTypes, toBase64 } from "../Ion";
import { Lob } from "./Lob";
export class <PERSON><PERSON>b extends Lob(IonTypes.BLOB) {
    constructor(data, annotations = []) {
        super(data, annotations);
    }
    toJSON() {
        return toBase64(this);
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeBlob(this);
    }
}
//# sourceMappingURL=Blob.js.map