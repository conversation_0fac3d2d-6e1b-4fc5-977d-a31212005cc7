import { IonTypes } from "../Ion";
import { Lob } from "./Lob";
export class <PERSON>lob extends Lob(IonTypes.CLOB) {
    constructor(bytes, annotations = []) {
        super(bytes, annotations);
    }
    writeTo(writer) {
        writer.setAnnotations(this.getAnnotations());
        writer.writeClob(this);
    }
    toJSON() {
        let encodedText = "";
        for (const byte of this) {
            if (byte >= 32 && byte <= 126) {
                encodedText += String.fromCharCode(byte);
                continue;
            }
            const hex = byte.toString(16);
            if (hex.length == 1) {
                encodedText += "\\u000" + hex;
            }
            else {
                encodedText += "\\u00" + hex;
            }
        }
        return encodedText;
    }
}
//# sourceMappingURL=Clob.js.map