/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { AbstractWriter } from "./AbstractWriter";
import { Decimal } from "./IonDecimal";
import { LocalSymbolTable } from "./IonLocalSymbolTable";
import { LowLevelBinaryWriter } from "./IonLowLevelBinaryWriter";
import { Timestamp } from "./IonTimestamp";
import { IonType } from "./IonType";
import { Writeable } from "./IonWriteable";
declare enum TypeCodes {
    NULL = 0,
    BOOL = 1,
    POSITIVE_INT = 2,
    NEGATIVE_INT = 3,
    FLOAT = 4,
    DECIMAL = 5,
    TIMESTAMP = 6,
    SYMBOL = 7,
    STRING = 8,
    CLOB = 9,
    BLOB = 10,
    LIST = 11,
    SEXP = 12,
    STRUCT = 13,
    ANNOTATION = 14
}
export declare class BinaryWriter extends AbstractWriter {
    private readonly symbolTable;
    private readonly writer;
    private datagram;
    private containers;
    private fieldName;
    private state;
    constructor(symbolTable: LocalSymbolTable, writeable: Writeable);
    getBytes(): Uint8Array;
    writeBlob(value: Uint8Array | null): void;
    writeBoolean(value: boolean | null): void;
    writeClob(value: Uint8Array | null): void;
    writeDecimal(value: Decimal | null): void;
    writeFloat32(value: number | null): void;
    writeFloat64(value: number | null): void;
    writeInt(value: number | bigint | null): void;
    writeNull(type: IonType): void;
    writeString(value: string | null): void;
    writeSymbol(value: string | null): void;
    writeTimestamp(value: Timestamp | null): void;
    stepIn(type: IonType): void;
    stepOut(): void;
    protected _isInStruct(): boolean;
    writeFieldName(fieldName: string): void;
    depth(): number;
    close(): void;
    private writeIvm;
    private encodeAnnotations;
    private getCurrentContainer;
    private addNode;
    private checkWriteValue;
    private checkClosed;
    private writeSymbolTable;
    private writeImport;
}
export interface Node {
    isContainer(): boolean;
    addChild(child: Node, name?: Uint8Array): void;
    write(): void;
    getLength(): number;
}
export declare abstract class AbstractNode implements Node {
    private readonly _writer;
    private readonly parent;
    private readonly _type;
    private readonly annotations;
    protected constructor(_writer: LowLevelBinaryWriter, parent: Node | null, _type: IonType, annotations: Uint8Array);
    get typeCode(): number;
    get writer(): LowLevelBinaryWriter;
    static getLengthLength(length: number): number;
    writeTypeDescriptorAndLength(typeCode: TypeCodes, isNull: boolean, length: number): void;
    getContainedValueLength(): number;
    abstract getValueLength(): number;
    getAnnotatedContainerLength(): number;
    getAnnotationsLength(): number;
    getLength(): number;
    writeAnnotations(): void;
    abstract isContainer(): boolean;
    abstract addChild(child: Node, name?: Uint8Array): void;
    abstract write(): void;
    private hasAnnotations;
}
export declare abstract class LeafNode extends AbstractNode {
    addChild(child: Node, name?: Uint8Array): void;
    isContainer(): boolean;
}
export declare class NullNode extends LeafNode {
    constructor(writer: LowLevelBinaryWriter, parent: Node | null, type: IonType, annotations: Uint8Array);
    write(): void;
    getValueLength(): number;
}
export {};
