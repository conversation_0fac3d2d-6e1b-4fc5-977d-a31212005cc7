/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { EOF } from "./IonConstants";
const SPAN_TYPE_STRING = 0;
const SPAN_TYPE_BINARY = 1;
const SPAN_TYPE_SUB_FLAG = 2;
const SPAN_TYPE_SUB_STRING = SPAN_TYPE_SUB_FLAG | SPAN_TYPE_STRING;
const SPAN_TYPE_SUB_BINARY = SPAN_TYPE_SUB_FLAG | SPAN_TYPE_BINARY;
const MAX_POS = 1024 * 1024 * 1024;
const LINE_FEED = 10;
const CARRAIGE_RETURN = 13;
const DEBUG_FLAG = true;
export class Span {
    constructor(_type) {
        this._type = _type;
    }
    static error() {
        throw new Error("span error");
    }
    write(b) {
        throw new Error("not implemented");
    }
}
export class StringSpan extends Span {
    constructor(src) {
        super(SPAN_TYPE_STRING);
        this._line = 1;
        this._src = src;
        this._limit = src.length;
        this._start = 0;
        this._pos = 0;
        this._line_start = 0;
        this._old_line_start = 0;
    }
    viewSource() {
        return this._src;
    }
    position() {
        return this._pos - this._start;
    }
    getRemaining() {
        return this._limit - this._pos;
    }
    setRemaining(r) {
        this._limit = r + this._pos;
    }
    is_empty() {
        return this._pos >= this._limit;
    }
    next() {
        let ch;
        if (this.is_empty()) {
            if (this._pos > MAX_POS) {
                throw new Error("span position is out of bounds");
            }
            this._pos++;
            return EOF;
        }
        ch = this._src.charCodeAt(this._pos);
        if (ch === CARRAIGE_RETURN) {
            if (this.peek() != LINE_FEED) {
                this._inc_line();
            }
        }
        else if (ch == LINE_FEED) {
            this._inc_line();
        }
        this._pos++;
        return ch;
    }
    _inc_line() {
        this._old_line_start = this._line_start;
        this._line++;
        this._line_start = this._pos;
    }
    unread(ch) {
        if (this._pos <= this._start) {
            Span.error();
        }
        this._pos--;
        if (ch < 0) {
            if (this.is_empty() != true) {
                Span.error();
            }
            return;
        }
        if (this._pos == this._line_start) {
            this._line_start = this._old_line_start;
            this._line--;
        }
        if (ch != this.peek()) {
            Span.error();
        }
    }
    peek() {
        return this.valueAt(this._pos);
    }
    skip(dist) {
        this._pos += dist;
        if (this._pos > this._limit) {
            this._pos = this._limit;
        }
    }
    valueAt(ii) {
        if (ii < this._start || ii >= this._limit) {
            return EOF;
        }
        return this._src.charCodeAt(ii);
    }
    chunk(length) {
        const tempStr = this._src.substr(this._pos, length);
        this._pos += length;
        return tempStr;
    }
    getCodePoint(index) {
        return this._src.codePointAt(index);
    }
    line_number() {
        return this._line;
    }
    offset() {
        return this._pos - this._line_start;
    }
    clone(start) {
        return new StringSpan(this._src.substr(this._pos));
    }
}
export class BinarySpan extends Span {
    constructor(src) {
        super(SPAN_TYPE_BINARY);
        this._src = src;
        this._limit = src.length;
        this._start = 0;
        this._pos = 0;
    }
    position() {
        return this._pos - this._start;
    }
    getRemaining() {
        return this._limit - this._pos;
    }
    setRemaining(r) {
        this._limit = r + this._pos;
    }
    is_empty() {
        return this._pos >= this._limit;
    }
    next() {
        if (this.is_empty()) {
            return EOF;
        }
        return this._src[this._pos++];
    }
    view(length) {
        if (this._pos + length > this._limit) {
            throw new Error("Unable to read " +
                length +
                " bytes (position: " +
                this.position() +
                ", limit: " +
                this._limit +
                ")");
        }
        return this._src.subarray(this._pos, (this._pos += length));
    }
    chunk(length) {
        return new Uint8Array(this.view(length));
    }
    unread(b) {
        if (this._pos <= this._start) {
            Span.error();
        }
        this._pos--;
        if (b == EOF) {
            if (this.is_empty() == false) {
                Span.error();
            }
        }
        if (b != this.peek()) {
            Span.error();
        }
    }
    peek() {
        if (this.is_empty()) {
            return EOF;
        }
        return this._src[this._pos];
    }
    skip(dist) {
        this._pos += dist;
        if (this._pos > this._limit) {
            throw new Error("Skipped over end of source.");
        }
    }
    valueAt(ii) {
        if (ii < this._start || ii >= this._limit) {
            return EOF;
        }
        return this._src[ii];
    }
    clone(start, len) {
        return new BinarySpan(this._src.subarray(this._pos));
    }
}
//# sourceMappingURL=IonSpan.js.map