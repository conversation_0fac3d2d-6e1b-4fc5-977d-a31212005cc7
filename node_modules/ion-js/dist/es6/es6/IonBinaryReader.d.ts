/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import IntSize from "./IntSize";
import { Catalog } from "./IonCatalog";
import { Decimal } from "./IonDecimal";
import { Reader } from "./IonReader";
import { BinarySpan } from "./IonSpan";
import { Timestamp } from "./IonTimestamp";
import { IonType } from "./IonType";
export declare class BinaryReader implements Reader {
    private _parser;
    private readonly _cat;
    private _symtab;
    private _raw_type;
    private _annotations;
    constructor(source: BinarySpan, catalog?: Catalog);
    position(): number;
    next(): IonType | null;
    stepIn(): void;
    stepOut(): void;
    type(): IonType | null;
    depth(): number;
    fieldName(): string | null;
    hasAnnotations(): boolean;
    annotations(): string[];
    getAnnotation(index: number): string;
    isNull(): boolean;
    uInt8ArrayValue(): Uint8Array | null;
    booleanValue(): boolean | null;
    decimalValue(): Decimal | null;
    bigIntValue(): bigint | null;
    intSize(): IntSize;
    numberValue(): number | null;
    stringValue(): string | null;
    timestampValue(): Timestamp | null;
    value(): any;
    private _loadAnnotations;
    private getSymbolString;
}
