/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { BigIntSerde } from "./BigIntSerde";
export class LowLevelBinaryWriter {
    constructor(writeable) {
        this.writeable = writeable;
    }
    static getSignedIntSize(value) {
        if (value === 0) {
            return 1;
        }
        const numberOfSignBits = 1;
        const magnitude = Math.abs(value);
        const numberOfMagnitudeBits = Math.ceil(Math.log2(magnitude + 1));
        const numberOfBits = numberOfMagnitudeBits + numberOfSignBits;
        return Math.ceil(numberOfBits / 8);
    }
    static getUnsignedIntSize(value) {
        if (typeof value === "bigint") {
            return BigIntSerde.getUnsignedIntSizeInBytes(value);
        }
        if (value === 0) {
            return 1;
        }
        const numberOfBits = Math.floor(Math.log2(value)) + 1;
        const numberOfBytes = Math.ceil(numberOfBits / 8);
        return numberOfBytes;
    }
    static getVariableLengthSignedIntSize(value) {
        const absoluteValue = Math.abs(value);
        if (absoluteValue === 0) {
            return 1;
        }
        const valueBits = Math.floor(Math.log2(absoluteValue)) + 1;
        const trailingStopBits = Math.floor(valueBits / 7);
        const leadingStopBit = 1;
        const signBit = 1;
        return Math.ceil((valueBits + trailingStopBits + leadingStopBit + signBit) / 8);
    }
    static getVariableLengthUnsignedIntSize(value) {
        if (value === 0) {
            return 1;
        }
        const valueBits = Math.floor(Math.log2(value)) + 1;
        const stopBits = Math.ceil(valueBits / 7);
        return Math.ceil((valueBits + stopBits) / 8);
    }
    writeSignedInt(originalValue) {
        const length = LowLevelBinaryWriter.getSignedIntSize(originalValue);
        let value = Math.abs(originalValue);
        const tempBuf = new Uint8Array(length);
        let i = tempBuf.length;
        while (value >= 128) {
            tempBuf[--i] = value & 0xff;
            value >>>= 8;
        }
        tempBuf[--i] = value & 0xff;
        if (1 / originalValue < 0) {
            tempBuf[0] |= 0x80;
        }
        this.writeable.writeBytes(tempBuf);
    }
    writeUnsignedInt(originalValue) {
        if (typeof originalValue === "bigint") {
            const encodedBytes = BigIntSerde.toUnsignedIntBytes(originalValue);
            this.writeable.writeBytes(encodedBytes);
            return;
        }
        const length = LowLevelBinaryWriter.getUnsignedIntSize(originalValue);
        const tempBuf = new Uint8Array(length);
        let value = originalValue;
        let i = tempBuf.length;
        while (value > 0) {
            tempBuf[--i] = value % 256;
            value = Math.trunc(value / 256);
        }
        this.writeable.writeBytes(tempBuf);
    }
    writeVariableLengthSignedInt(originalValue) {
        const tempBuf = new Uint8Array(LowLevelBinaryWriter.getVariableLengthSignedIntSize(originalValue));
        let value = Math.abs(originalValue);
        let i = tempBuf.length - 1;
        while (value >= 64) {
            tempBuf[i--] = value & 0x7f;
            value >>>= 7;
        }
        tempBuf[i] = value;
        if (1 / originalValue < 0) {
            tempBuf[i] |= 0x40;
        }
        tempBuf[tempBuf.length - 1] |= 0x80;
        this.writeable.writeBytes(tempBuf);
    }
    writeVariableLengthUnsignedInt(originalValue) {
        const tempBuf = new Uint8Array(LowLevelBinaryWriter.getVariableLengthUnsignedIntSize(originalValue));
        let value = originalValue;
        let i = tempBuf.length;
        tempBuf[--i] = (value & 0x7f) | 0x80;
        value >>>= 7;
        while (value > 0) {
            tempBuf[--i] = value & 0x7f;
            value >>>= 7;
        }
        this.writeable.writeBytes(tempBuf);
    }
    writeByte(byte) {
        this.writeable.writeByte(byte);
    }
    writeBytes(bytes) {
        this.writeable.writeBytes(bytes);
    }
    getBytes() {
        return this.writeable.getBytes();
    }
}
//# sourceMappingURL=IonLowLevelBinaryWriter.js.map