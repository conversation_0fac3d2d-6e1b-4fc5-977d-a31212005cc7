/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { AbstractWriter } from "./AbstractWriter";
import { CharCodes, ClobEscapes, escape, isIdentifier, isOperator, is_keyword, StringEscapes, SymbolEscapes, toBase64, } from "./IonText";
import { IonTypes } from "./IonTypes";
import { encodeUtf8 } from "./IonUnicode";
import { _assertDefined } from "./util";
export var State;
(function (State) {
    State[State["VALUE"] = 0] = "VALUE";
    State[State["STRUCT_FIELD"] = 1] = "STRUCT_FIELD";
})(State || (State = {}));
export class Context {
    constructor(myType) {
        this.state = myType === IonTypes.STRUCT ? State.STRUCT_FIELD : State.VALUE;
        this.clean = true;
        this.containerType = myType;
    }
}
export class TextWriter extends AbstractWriter {
    constructor(writeable) {
        super();
        this.writeable = writeable;
        this._floatSerializer = (value) => {
            TextWriter._serializeFloat(this, value);
        };
        this.containerContext = [new Context(null)];
    }
    get isTopLevel() {
        return this.depth() === 0;
    }
    get currentContainer() {
        return this.containerContext[this.depth()];
    }
    static _serializeFloat(writer, value) {
        let text;
        if (value === Number.POSITIVE_INFINITY) {
            text = "+inf";
        }
        else if (value === Number.NEGATIVE_INFINITY) {
            text = "-inf";
        }
        else if (Object.is(value, Number.NaN)) {
            text = "nan";
        }
        else if (Object.is(value, -0)) {
            text = "-0e0";
        }
        else {
            text = value.toExponential();
            const plusSignIndex = text.lastIndexOf("+");
            if (plusSignIndex > -1) {
                text = text.slice(0, plusSignIndex) + text.slice(plusSignIndex + 1);
            }
        }
        writer.writeUtf8(text);
    }
    getBytes() {
        return this.writeable.getBytes();
    }
    writeBlob(value) {
        _assertDefined(value);
        this._serializeValue(IonTypes.BLOB, value, (value) => {
            this.writeable.writeBytes(encodeUtf8("{{" + toBase64(value) + "}}"));
        });
    }
    writeBoolean(value) {
        _assertDefined(value);
        this._serializeValue(IonTypes.BOOL, value, (value) => {
            this.writeUtf8(value ? "true" : "false");
        });
    }
    writeClob(value) {
        _assertDefined(value);
        this._serializeValue(IonTypes.CLOB, value, (value) => {
            let hexStr;
            this.writeUtf8('{{"');
            for (let i = 0; i < value.length; i++) {
                const c = value[i];
                if (c > 127 && c < 256) {
                    hexStr = "\\x" + c.toString(16);
                    for (let j = 0; j < hexStr.length; j++) {
                        this.writeable.writeByte(hexStr.charCodeAt(j));
                    }
                }
                else {
                    const escape = ClobEscapes[c];
                    if (escape === undefined) {
                        if (c < 32) {
                            hexStr = "\\x" + c.toString(16);
                            for (let j = 0; j < hexStr.length; j++) {
                                this.writeable.writeByte(hexStr.charCodeAt(j));
                            }
                        }
                        else {
                            this.writeable.writeByte(c);
                        }
                    }
                    else {
                        this.writeable.writeBytes(new Uint8Array(escape));
                    }
                }
            }
            this.writeUtf8('"}}');
        });
    }
    writeDecimal(value) {
        _assertDefined(value);
        this._serializeValue(IonTypes.DECIMAL, value, (value) => {
            let s = "";
            let coefficient = value.getCoefficient();
            if (coefficient < 0n) {
                coefficient = -coefficient;
            }
            if (value.isNegative()) {
                s += "-";
            }
            const exponent = value.getExponent();
            const scale = -exponent;
            if (exponent == 0) {
                s += coefficient.toString() + ".";
            }
            else if (exponent < 0) {
                const significantDigits = coefficient.toString().length;
                const adjustedExponent = significantDigits - 1 - scale;
                if (adjustedExponent >= 0) {
                    const wholeDigits = significantDigits - scale;
                    s += coefficient.toString().substring(0, wholeDigits);
                    s += ".";
                    s += coefficient.toString().substring(wholeDigits, significantDigits);
                }
                else if (adjustedExponent >= -6) {
                    s += "0.";
                    s += "00000".substring(0, scale - significantDigits);
                    s += coefficient.toString();
                }
                else {
                    s += coefficient.toString();
                    s += "d-";
                    s += scale.toString();
                }
            }
            else {
                s += coefficient.toString() + "d" + exponent;
            }
            this.writeUtf8(s);
        });
    }
    _isInStruct() {
        return this.currentContainer.containerType === IonTypes.STRUCT;
    }
    writeFieldName(fieldName) {
        _assertDefined(fieldName);
        if (this.currentContainer.containerType !== IonTypes.STRUCT) {
            throw new Error("Cannot write field name outside of a struct");
        }
        if (this.currentContainer.state !== State.STRUCT_FIELD) {
            throw new Error("Expecting a struct value");
        }
        if (!this.currentContainer.clean) {
            this.writeable.writeByte(CharCodes.COMMA);
        }
        this.writeSymbolToken(fieldName);
        this.writeable.writeByte(CharCodes.COLON);
        this.currentContainer.state = State.VALUE;
    }
    writeFloat32(value) {
        _assertDefined(value);
        this._writeFloat(value);
    }
    writeFloat64(value) {
        _assertDefined(value);
        this._writeFloat(value);
    }
    writeInt(value) {
        _assertDefined(value);
        this._serializeValue(IonTypes.INT, value, (value) => {
            this.writeUtf8(value.toString(10));
        });
    }
    _writeNull(type) {
        if (type === IonTypes.NULL) {
            this.writeUtf8("null");
        }
        else {
            this.writeUtf8("null." + type.name);
        }
    }
    writeNull(type) {
        if (type === undefined || type === null) {
            type = IonTypes.NULL;
        }
        this.handleSeparator();
        this.writeAnnotations();
        this._writeNull(type);
        if (this.currentContainer.containerType === IonTypes.STRUCT) {
            this.currentContainer.state = State.STRUCT_FIELD;
        }
    }
    writeString(value) {
        _assertDefined(value);
        this._serializeValue(IonTypes.STRING, value, (value) => {
            this.writeable.writeBytes(encodeUtf8('"' + escape(value, StringEscapes) + '"'));
        });
    }
    writeSymbol(value) {
        _assertDefined(value);
        this._serializeValue(IonTypes.SYMBOL, value, (value) => {
            this.writeSymbolToken(value);
        });
    }
    writeTimestamp(value) {
        _assertDefined(value);
        this._serializeValue(IonTypes.TIMESTAMP, value, (value) => {
            this.writeUtf8(value.toString());
        });
    }
    stepIn(type) {
        if (this.currentContainer.state === State.STRUCT_FIELD) {
            throw new Error(`Started writing a ${this.currentContainer.containerType.name} inside a struct"
                + " without writing the field name first. Call writeFieldName(string) with the desired name"
                + " before calling stepIn(${this.currentContainer.containerType.name}).`);
        }
        switch (type) {
            case IonTypes.LIST:
                this.writeContainer(type, CharCodes.LEFT_BRACKET);
                break;
            case IonTypes.SEXP:
                this.writeContainer(type, CharCodes.LEFT_PARENTHESIS);
                break;
            case IonTypes.STRUCT:
                if (this._annotations !== undefined &&
                    this._annotations[0] === "$ion_symbol_table" &&
                    this.depth() === 0) {
                    throw new Error("Unable to alter symbol table context, it allows invalid ion to be written.");
                }
                this.writeContainer(type, CharCodes.LEFT_BRACE);
                break;
            default:
                throw new Error("Unrecognized container type");
        }
    }
    stepOut() {
        const currentContainer = this.containerContext.pop();
        if (!currentContainer || !currentContainer.containerType) {
            throw new Error("Can't step out when not in a container");
        }
        else if (currentContainer.containerType === IonTypes.STRUCT &&
            currentContainer.state === State.VALUE) {
            throw new Error("Expecting a struct value");
        }
        switch (currentContainer.containerType) {
            case IonTypes.LIST:
                this.writeable.writeByte(CharCodes.RIGHT_BRACKET);
                break;
            case IonTypes.SEXP:
                this.writeable.writeByte(CharCodes.RIGHT_PARENTHESIS);
                break;
            case IonTypes.STRUCT:
                this.writeable.writeByte(CharCodes.RIGHT_BRACE);
                break;
            default:
                throw new Error("Unexpected container TypeCode");
        }
    }
    close() {
        if (this.depth() > 0) {
            throw new Error("Writer has one or more open containers; call stepOut() for each container prior to close()");
        }
    }
    depth() {
        return this.containerContext.length - 1;
    }
    _serializeValue(type, value, serialize) {
        if (this.currentContainer.state === State.STRUCT_FIELD) {
            throw new Error("Expecting a struct field");
        }
        if (value === null) {
            this.writeNull(type);
            return;
        }
        this.handleSeparator();
        this.writeAnnotations();
        serialize(value);
        if (this.currentContainer.containerType === IonTypes.STRUCT) {
            this.currentContainer.state = State.STRUCT_FIELD;
        }
    }
    writeContainer(type, openingCharacter) {
        if (this.currentContainer.containerType === IonTypes.STRUCT &&
            this.currentContainer.state === State.VALUE) {
            this.currentContainer.state = State.STRUCT_FIELD;
        }
        this.handleSeparator();
        this.writeAnnotations();
        this.writeable.writeByte(openingCharacter);
        this._stepIn(type);
    }
    handleSeparator() {
        if (this.depth() === 0) {
            if (this.currentContainer.clean) {
                this.currentContainer.clean = false;
            }
            else {
                this.writeable.writeByte(CharCodes.LINE_FEED);
            }
        }
        else {
            if (this.currentContainer.clean) {
                this.currentContainer.clean = false;
            }
            else {
                switch (this.currentContainer.containerType) {
                    case IonTypes.LIST:
                        this.writeable.writeByte(CharCodes.COMMA);
                        break;
                    case IonTypes.SEXP:
                        this.writeable.writeByte(CharCodes.SPACE);
                        break;
                    default:
                }
            }
        }
    }
    writeUtf8(s) {
        this.writeable.writeBytes(encodeUtf8(s));
    }
    writeAnnotations() {
        for (const annotation of this._annotations) {
            this.writeSymbolToken(annotation);
            this.writeUtf8("::");
        }
        this._clearAnnotations();
    }
    _stepIn(container) {
        this.containerContext.push(new Context(container));
    }
    writeSymbolToken(s) {
        if (s.length === 0 ||
            is_keyword(s) ||
            this.isSid(s) ||
            (!isIdentifier(s) && !isOperator(s)) ||
            (isOperator(s) && this.currentContainer.containerType != IonTypes.SEXP)) {
            this.writeable.writeBytes(encodeUtf8("'" + escape(s, SymbolEscapes) + "'"));
        }
        else {
            this.writeUtf8(s);
        }
    }
    _writeFloat(value) {
        this._serializeValue(IonTypes.FLOAT, value, this._floatSerializer);
    }
    isSid(s) {
        if (s.length > 1 && s.charAt(0) === "$".charAt(0)) {
            const t = s.substr(1, s.length);
            return +t === +t;
        }
        return false;
    }
}
//# sourceMappingURL=IonTextWriter.js.map