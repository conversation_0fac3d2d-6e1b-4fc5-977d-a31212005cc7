/*!
 * Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
export class SymbolToken {
    constructor(text, sid = SymbolToken._UNKNOWN_SYMBOL_ID) {
        this.text = text;
        this.sid = sid;
    }
    getText() {
        return this.text;
    }
    getSid() {
        return this.sid;
    }
}
SymbolToken._UNKNOWN_SYMBOL_ID = -1;
//# sourceMappingURL=IonSymbolToken.js.map