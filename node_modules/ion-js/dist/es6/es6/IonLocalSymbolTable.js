/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { getSystemSymbolTableImport } from "./IonSystemSymbolTable";
export class LocalSymbolTable {
    constructor(theImport, symbols = []) {
        this.index = Object.create(null);
        this._symbols = [];
        if (theImport === null) {
            this._import = getSystemSymbolTableImport();
        }
        else {
            this._import = theImport;
        }
        this.offset = this._import.offset + this._import.length;
        for (const symbol_ of symbols) {
            this.assignSymbolId(symbol_);
        }
    }
    get symbols() {
        return this._symbols;
    }
    get maxId() {
        return this.offset + this._symbols.length - 1;
    }
    get import() {
        return this._import;
    }
    getSymbolId(symbol_) {
        return this._import.getSymbolId(symbol_) || this.index[symbol_];
    }
    addSymbol(symbol_) {
        if (symbol_ !== null) {
            const existingSymbolId = this.getSymbolId(symbol_);
            if (existingSymbolId !== undefined) {
                return existingSymbolId;
            }
        }
        const symbolId = this.offset + this.symbols.length;
        this.symbols.push(symbol_);
        if (symbol_ !== null) {
            this.index[symbol_] = symbolId;
        }
        return symbolId;
    }
    assignSymbolId(symbol) {
        const symbolId = this.offset + this.symbols.length;
        this.symbols.push(symbol);
        if (symbol !== null && this.getSymbolId(symbol) === undefined) {
            this.index[symbol] = symbolId;
        }
        return symbolId;
    }
    getSymbolText(symbolId) {
        if (symbolId > this.maxId) {
            throw new Error("Symbol $" + symbolId.toString() + " greater than maxID.");
        }
        const importedSymbol = this.import.getSymbolText(symbolId);
        if (importedSymbol !== undefined) {
            return importedSymbol;
        }
        const index = symbolId - this.offset;
        return this.symbols[index];
    }
    numberOfSymbols() {
        return this._symbols.length;
    }
}
export function defaultLocalSymbolTable() {
    return new LocalSymbolTable(getSystemSymbolTableImport());
}
//# sourceMappingURL=IonLocalSymbolTable.js.map