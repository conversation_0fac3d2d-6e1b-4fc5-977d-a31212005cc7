/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
define(["require", "exports", "./IonText"], function (require, exports, IonText_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Symbol = void 0;
    class Symbol {
        constructor(id, val) {
            this.sid = id;
            this.name = val;
        }
        toString() {
            const s = "sym::{id:" + IonText_1.asAscii(this.sid) + ',val:"' + IonText_1.as<PERSON><PERSON>i(this.name) + '"';
            return s;
        }
    }
    exports.Symbol = Symbol;
});
//# sourceMappingURL=IonSymbol.js.map