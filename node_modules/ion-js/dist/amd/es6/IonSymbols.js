/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
define(["require", "exports", "./IonImport", "./IonLocalSymbolTable", "./IonSubstituteSymbolTable", "./IonSystemSymbolTable", "./Ion"], function (require, exports, IonImport_1, IonLocalSymbolTable_1, IonSubstituteSymbolTable_1, IonSystemSymbolTable_1, Ion_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.makeSymbolTable = exports.ion_symbol_table_sid = exports.ion_symbol_table = void 0;
    exports.ion_symbol_table = "$ion_symbol_table";
    exports.ion_symbol_table_sid = 3;
    const empty_struct = {};
    function load_imports(reader, catalog) {
        let import_ = IonSystemSymbolTable_1.getSystemSymbolTableImport();
        reader.stepIn();
        while (reader.next()) {
            reader.stepIn();
            let name = null;
            let version = 1;
            let maxId = null;
            while (reader.next()) {
                switch (reader.fieldName()) {
                    case "name":
                        name = reader.stringValue();
                        break;
                    case "version":
                        version = reader.numberValue();
                        break;
                    case "max_id":
                        maxId = reader.numberValue();
                }
            }
            if (version === null || version < 1) {
                version = 1;
            }
            if (name && name !== "$ion") {
                let symbolTable = catalog.getVersion(name, version);
                if (!symbolTable) {
                    if (maxId === undefined) {
                        throw new Error(`No exact match found when trying to import symbol table ${name} version ${version}`);
                    }
                    else {
                        symbolTable = catalog.getTable(name);
                    }
                }
                if (!symbolTable) {
                    symbolTable = new IonSubstituteSymbolTable_1.SubstituteSymbolTable(maxId);
                }
                import_ = new IonImport_1.Import(import_, symbolTable, maxId);
            }
            reader.stepOut();
        }
        reader.stepOut();
        return import_;
    }
    function load_symbols(reader) {
        const symbols = [];
        reader.stepIn();
        while (reader.next()) {
            symbols.push(reader.stringValue());
        }
        reader.stepOut();
        return symbols;
    }
    function makeSymbolTable(catalog, reader, currentSymbolTable) {
        let import_ = null;
        let symbols = [];
        let foundSymbols = false;
        let foundImports = false;
        let foundLstAppend = false;
        reader.stepIn();
        while (reader.next()) {
            switch (reader.fieldName()) {
                case "imports":
                    if (foundImports) {
                        throw new Error("Multiple import fields found.");
                    }
                    let ion_type = reader.type();
                    if (ion_type === Ion_1.IonTypes.SYMBOL &&
                        reader.stringValue() === exports.ion_symbol_table) {
                        import_ = currentSymbolTable.import;
                        let symbols_ = symbols;
                        symbols = currentSymbolTable.symbols;
                        symbols.push(...symbols_);
                        foundLstAppend = true;
                    }
                    else if (ion_type === Ion_1.IonTypes.LIST) {
                        import_ = load_imports(reader, catalog);
                    }
                    else {
                        throw new Error(`Expected import field name to be a list or symbol found ${ion_type}`);
                    }
                    foundImports = true;
                    break;
                case "symbols":
                    if (foundSymbols) {
                        throw new Error("Multiple symbol fields found.");
                    }
                    if (foundLstAppend) {
                        symbols.push(...load_symbols(reader));
                    }
                    else {
                        symbols = load_symbols(reader);
                    }
                    foundSymbols = true;
                    break;
            }
        }
        reader.stepOut();
        return new IonLocalSymbolTable_1.LocalSymbolTable(import_, symbols);
    }
    exports.makeSymbolTable = makeSymbolTable;
});
//# sourceMappingURL=IonSymbols.js.map