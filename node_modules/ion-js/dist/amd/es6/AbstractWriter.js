/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
define(["require", "exports", "./IonTypes"], function (require, exports, IonTypes_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.AbstractWriter = void 0;
    class AbstractWriter {
        constructor() {
            this._annotations = [];
        }
        addAnnotation(annotation) {
            if (!this._isString(annotation)) {
                throw new Error("Annotation must be of type string.");
            }
            this._annotations.push(annotation);
        }
        setAnnotations(annotations) {
            if (annotations === undefined || annotations === null) {
                throw new Error("Annotations were undefined or null.");
            }
            else if (!this._validateAnnotations(annotations)) {
                throw new Error("Annotations must be of type string[].");
            }
            else {
                this._annotations = annotations;
            }
        }
        writeValues(reader) {
            this._writeValues(reader);
        }
        writeValue(reader) {
            this._writeValue(reader);
        }
        _clearAnnotations() {
            this._annotations = [];
        }
        _writeValues(reader) {
            let type = reader.type();
            if (type === null) {
                type = reader.next();
            }
            while (type !== null) {
                this._writeValue(reader);
                type = reader.next();
            }
        }
        _writeValue(reader) {
            const type = reader.type();
            if (type === null) {
                return;
            }
            if (this._isInStruct()) {
                const fieldName = reader.fieldName();
                if (fieldName === null) {
                    throw new Error("Cannot call writeValue() when the Writer is in a Struct but the Reader is not.");
                }
                this.writeFieldName(fieldName);
            }
            this.setAnnotations(reader.annotations());
            if (reader.isNull()) {
                this.writeNull(type);
                return;
            }
            switch (type) {
                case IonTypes_1.IonTypes.BOOL:
                    this.writeBoolean(reader.booleanValue());
                    break;
                case IonTypes_1.IonTypes.INT:
                    this.writeInt(reader.bigIntValue());
                    break;
                case IonTypes_1.IonTypes.FLOAT:
                    this.writeFloat64(reader.numberValue());
                    break;
                case IonTypes_1.IonTypes.DECIMAL:
                    this.writeDecimal(reader.decimalValue());
                    break;
                case IonTypes_1.IonTypes.TIMESTAMP:
                    this.writeTimestamp(reader.timestampValue());
                    break;
                case IonTypes_1.IonTypes.SYMBOL:
                    this.writeSymbol(reader.stringValue());
                    break;
                case IonTypes_1.IonTypes.STRING:
                    this.writeString(reader.stringValue());
                    break;
                case IonTypes_1.IonTypes.CLOB:
                    this.writeClob(reader.uInt8ArrayValue());
                    break;
                case IonTypes_1.IonTypes.BLOB:
                    this.writeBlob(reader.uInt8ArrayValue());
                    break;
                case IonTypes_1.IonTypes.LIST:
                    this.stepIn(IonTypes_1.IonTypes.LIST);
                    break;
                case IonTypes_1.IonTypes.SEXP:
                    this.stepIn(IonTypes_1.IonTypes.SEXP);
                    break;
                case IonTypes_1.IonTypes.STRUCT:
                    this.stepIn(IonTypes_1.IonTypes.STRUCT);
                    break;
                default:
                    throw new Error("Unrecognized type " + (type !== null ? type.name : type));
            }
            if (type.isContainer) {
                reader.stepIn();
                this._writeValues(reader);
                this.stepOut();
                reader.stepOut();
            }
        }
        _validateAnnotations(input) {
            if (!Array.isArray(input)) {
                return false;
            }
            for (let i = 0; i < input.length; i++) {
                if (!this._isString(input[i])) {
                    return false;
                }
            }
            return true;
        }
        _isString(input) {
            return typeof input === "string";
        }
    }
    exports.AbstractWriter = AbstractWriter;
});
//# sourceMappingURL=AbstractWriter.js.map