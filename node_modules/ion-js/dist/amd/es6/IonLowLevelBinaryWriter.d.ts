/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { Writeable } from "./IonWriteable";
export declare class LowLevelBinaryWriter {
    private readonly writeable;
    constructor(writeable: Writeable);
    static getSignedIntSize(value: number): number;
    static getUnsignedIntSize(value: number | bigint): number;
    static getVariableLengthSignedIntSize(value: number): number;
    static getVariableLengthUnsignedIntSize(value: number): number;
    writeSignedInt(originalValue: number): void;
    writeUnsignedInt(originalValue: number | bigint): void;
    writeVariableLengthSignedInt(originalValue: number): void;
    writeVariableLengthUnsignedInt(originalValue: number): void;
    writeByte(byte: number): void;
    writeBytes(bytes: Uint8Array): void;
    getBytes(): Uint8Array;
}
