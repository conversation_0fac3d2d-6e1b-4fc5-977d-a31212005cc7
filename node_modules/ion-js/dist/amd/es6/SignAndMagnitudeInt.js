/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
define(["require", "exports"], function (require, exports) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    class SignAndMagnitudeInt {
        constructor(_magnitude, _isNegative = _magnitude < 0n) {
            this._magnitude = _magnitude;
            this._isNegative = _isNegative;
        }
        get magnitude() {
            return this._magnitude;
        }
        get isNegative() {
            return this._isNegative;
        }
        static fromNumber(value) {
            const isNegative = value < 0 || Object.is(value, -0);
            const absoluteValue = Math.abs(value);
            const magnitude = BigInt(absoluteValue);
            return new SignAndMagnitudeInt(magnitude, isNegative);
        }
        equals(other) {
            return (this._magnitude === other._magnitude &&
                this._isNegative === other._isNegative);
        }
    }
    exports.default = SignAndMagnitudeInt;
});
//# sourceMappingURL=SignAndMagnitudeInt.js.map