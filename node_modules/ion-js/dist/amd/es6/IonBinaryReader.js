/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
define(["require", "exports", "./IntSize", "./IonCatalog", "./IonConstants", "./IonLocalSymbolTable", "./IonParserBinaryRaw", "./IonSymbols", "./IonTypes", "./util"], function (require, exports, IntSize_1, IonCatalog_1, IonConstants_1, IonLocalSymbolTable_1, IonParserBinaryRaw_1, IonSymbols_1, IonTypes_1, util_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.BinaryReader = void 0;
    IntSize_1 = __importDefault(IntSize_1);
    const BOC = -2;
    const EOF = -1;
    const TB_NULL = 0;
    const TB_BOOL = 1;
    const TB_INT = 2;
    const TB_NEG_INT = 3;
    const TB_FLOAT = 4;
    const TB_DECIMAL = 5;
    const TB_TIMESTAMP = 6;
    const TB_SYMBOL = 7;
    const TB_STRING = 8;
    const TB_CLOB = 9;
    const TB_BLOB = 10;
    const TB_LIST = 11;
    const TB_SEXP = 12;
    const TB_STRUCT = 13;
    function get_ion_type(t) {
        switch (t) {
            case TB_NULL:
                return IonTypes_1.IonTypes.NULL;
            case TB_BOOL:
                return IonTypes_1.IonTypes.BOOL;
            case TB_INT:
                return IonTypes_1.IonTypes.INT;
            case TB_NEG_INT:
                return IonTypes_1.IonTypes.INT;
            case TB_FLOAT:
                return IonTypes_1.IonTypes.FLOAT;
            case TB_DECIMAL:
                return IonTypes_1.IonTypes.DECIMAL;
            case TB_TIMESTAMP:
                return IonTypes_1.IonTypes.TIMESTAMP;
            case TB_SYMBOL:
                return IonTypes_1.IonTypes.SYMBOL;
            case TB_STRING:
                return IonTypes_1.IonTypes.STRING;
            case TB_CLOB:
                return IonTypes_1.IonTypes.CLOB;
            case TB_BLOB:
                return IonTypes_1.IonTypes.BLOB;
            case TB_LIST:
                return IonTypes_1.IonTypes.LIST;
            case TB_SEXP:
                return IonTypes_1.IonTypes.SEXP;
            case TB_STRUCT:
                return IonTypes_1.IonTypes.STRUCT;
            default:
                return null;
        }
    }
    class BinaryReader {
        constructor(source, catalog) {
            this._annotations = null;
            this._parser = new IonParserBinaryRaw_1.ParserBinaryRaw(source);
            this._cat = catalog ? catalog : new IonCatalog_1.Catalog();
            this._symtab = IonLocalSymbolTable_1.defaultLocalSymbolTable();
            this._raw_type = BOC;
        }
        position() {
            return this._parser.source().position();
        }
        next() {
            this._annotations = null;
            if (this._raw_type === EOF) {
                return null;
            }
            for (this._raw_type = this._parser.next(); this.depth() === 0; this._raw_type = this._parser.next()) {
                if (this._raw_type === TB_SYMBOL) {
                    const raw = this._parser._getSid();
                    if (raw !== IonConstants_1.IVM.sid) {
                        break;
                    }
                    this._symtab = IonLocalSymbolTable_1.defaultLocalSymbolTable();
                }
                else if (this._raw_type === TB_STRUCT) {
                    if (!this._parser.hasAnnotations()) {
                        break;
                    }
                    if (this._parser.getAnnotation(0) !== IonSymbols_1.ion_symbol_table_sid) {
                        break;
                    }
                    this._symtab = IonSymbols_1.makeSymbolTable(this._cat, this, this._symtab);
                }
                else {
                    break;
                }
            }
            return get_ion_type(this._raw_type);
        }
        stepIn() {
            if (!get_ion_type(this._raw_type).isContainer) {
                throw new Error("Can't step in to a scalar value");
            }
            this._parser.stepIn();
            this._raw_type = BOC;
        }
        stepOut() {
            this._parser.stepOut();
            this._raw_type = BOC;
        }
        type() {
            return get_ion_type(this._raw_type);
        }
        depth() {
            return this._parser.depth();
        }
        fieldName() {
            return this.getSymbolString(this._parser.getFieldId());
        }
        hasAnnotations() {
            return this._parser.hasAnnotations();
        }
        annotations() {
            this._loadAnnotations();
            return this._annotations !== null ? this._annotations : [];
        }
        getAnnotation(index) {
            this._loadAnnotations();
            return this._annotations[index];
        }
        isNull() {
            return this._raw_type === TB_NULL || this._parser.isNull();
        }
        uInt8ArrayValue() {
            return this._parser.uInt8ArrayValue();
        }
        booleanValue() {
            return this._parser.booleanValue();
        }
        decimalValue() {
            return this._parser.decimalValue();
        }
        bigIntValue() {
            return this._parser.bigIntValue();
        }
        intSize() {
            if (util_1.isSafeInteger(this.bigIntValue())) {
                return IntSize_1.default.Number;
            }
            return IntSize_1.default.BigInt;
        }
        numberValue() {
            return this._parser.numberValue();
        }
        stringValue() {
            const t = this;
            const p = t._parser;
            switch (get_ion_type(t._raw_type)) {
                case IonTypes_1.IonTypes.NULL:
                    return null;
                case IonTypes_1.IonTypes.STRING:
                    if (this.isNull()) {
                        return null;
                    }
                    return p.stringValue();
                case IonTypes_1.IonTypes.SYMBOL:
                    if (this.isNull()) {
                        return null;
                    }
                    const sid = p._getSid();
                    if (sid !== null) {
                        return this.getSymbolString(sid);
                    }
            }
            throw new Error("Current value is not a string or symbol.");
        }
        timestampValue() {
            return this._parser.timestampValue();
        }
        value() {
            const type = this.type();
            if (type && type.isContainer) {
                if (this.isNull()) {
                    return null;
                }
                throw new Error("Unable to provide a value for " + type.name + " containers.");
            }
            switch (type) {
                case IonTypes_1.IonTypes.NULL:
                    return null;
                case IonTypes_1.IonTypes.BLOB:
                case IonTypes_1.IonTypes.CLOB:
                    return this.uInt8ArrayValue();
                case IonTypes_1.IonTypes.BOOL:
                    return this.booleanValue();
                case IonTypes_1.IonTypes.DECIMAL:
                    return this.decimalValue();
                case IonTypes_1.IonTypes.INT:
                    return this.bigIntValue();
                case IonTypes_1.IonTypes.FLOAT:
                    return this.numberValue();
                case IonTypes_1.IonTypes.STRING:
                case IonTypes_1.IonTypes.SYMBOL:
                    return this.stringValue();
                case IonTypes_1.IonTypes.TIMESTAMP:
                    return this.timestampValue();
                default:
                    throw new Error("There is no current value.");
            }
        }
        _loadAnnotations() {
            if (this._annotations === null) {
                this._annotations = [];
                this._parser.getAnnotations().forEach((id) => {
                    this._annotations.push(this.getSymbolString(id));
                });
            }
        }
        getSymbolString(symbolId) {
            let s = null;
            if (symbolId === null) {
                return null;
            }
            if (symbolId > 0) {
                s = this._symtab.getSymbolText(symbolId);
                if (s === undefined) {
                    throw new Error("symbol is unresolvable");
                }
            }
            else if (symbolId === 0) {
                throw new Error("Symbol ID zero is unsupported");
            }
            else if (symbolId < 0) {
                throw new Error("Negative symbol ID: " + symbolId + " is illegal.");
            }
            return s;
        }
    }
    exports.BinaryReader = BinaryReader;
});
//# sourceMappingURL=IonBinaryReader.js.map