/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { Decimal } from "./IonDecimal";
import { BinarySpan } from "./IonSpan";
import { Timestamp } from "./IonTimestamp";
import { IonType } from "./IonType";
import SignAndMagnitudeInt from "./SignAndMagnitudeInt";
export declare class ParserBinaryRaw {
    private _in;
    private _raw_type;
    private _len;
    private _curr;
    private _null;
    private _fid;
    private _as;
    private _ae;
    private _a;
    private _ts;
    private _in_struct;
    constructor(source: BinarySpan);
    static _readFloatFrom(input: BinarySpan, numberOfBytes: any): number | null;
    static _readVarUnsignedIntFrom(input: BinarySpan): number;
    static _readVarSignedIntFrom(input: BinarySpan): number;
    static _readSignedIntFrom(input: BinarySpan, numberOfBytes: number): SignAndMagnitudeInt;
    static _readUnsignedIntAsBigIntFrom(input: BinarySpan, numberOfBytes: number): bigint;
    static _readUnsignedIntAsNumberFrom(input: BinarySpan, numberOfBytes: number): number;
    private static readDecimalValueFrom;
    source(): BinarySpan;
    next(): any;
    stepIn(): void;
    stepOut(): void;
    isNull(): boolean;
    depth(): number;
    getFieldId(): number | null;
    hasAnnotations(): boolean;
    getAnnotations(): any;
    getAnnotation(index: number): any;
    ionType(): IonType;
    _getSid(): number | null;
    byteValue(): Uint8Array | null;
    uInt8ArrayValue(): Uint8Array | null;
    booleanValue(): boolean | null;
    decimalValue(): Decimal | null;
    bigIntValue(): bigint | null;
    numberValue(): number | null;
    stringValue(): string | null;
    timestampValue(): Timestamp | null;
    private read_binary_float;
    private readVarUnsignedInt;
    private readVarSignedInt;
    private readUnsignedIntAsBigInt;
    private readUnsignedIntAsNumber;
    private read_decimal_value;
    private read_timestamp_value;
    private read_string_value;
    private clear_value;
    private load_length;
    private load_next;
    private load_annotations;
    private load_ivm;
    private load_annotation_values;
    private _readIntegerMagnitude;
    private load_value;
}
