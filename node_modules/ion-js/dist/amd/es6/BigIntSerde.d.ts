/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
export declare class BigIntSerde {
    private static readonly SERIALIZED_BIGINT_SIZES_TO_PRECOMPUTE;
    private static readonly BITS_PER_BYTE;
    private static readonly BYTE_MAX_VALUE;
    private static readonly SIZE_THRESHOLDS;
    static toSignedIntBytes(value: bigint, isNegative: boolean): Uint8Array;
    static fromUnsignedBytes(bytes: Uint8Array): bigint;
    static toUnsignedIntBytes(value: bigint): Uint8Array;
    static getUnsignedIntSizeInBytes(value: bigint): number;
    private static calculateSizeThresholds;
    private static calculateSizeThreshold;
}
