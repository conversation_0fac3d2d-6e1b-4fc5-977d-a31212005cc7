define(["require", "exports", "../Ion", "./FromJsConstructor", "./Value"], function (require, exports, Ion_1, FromJsConstructor_1, Value_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Null = void 0;
    class Null extends Value_1.Value(Object, Ion_1.IonTypes.NULL, FromJsConstructor_1.FromJsConstructor.NONE) {
        constructor(ionType = Ion_1.IonTypes.NULL, annotations = []) {
            super();
            this._ionType = ionType;
            this._setAnnotations(annotations);
        }
        static _operationIsSupported(ionType, operation) {
            return Null._supportedIonTypesByOperation.get(operation).has(ionType);
        }
        isNull() {
            return true;
        }
        _convertToJsNull(operation) {
            if (Null._operationIsSupported(this.getType(), operation)) {
                return null;
            }
            throw new Error(`${operation}() is not supported by Ion type ${this.getType().name}`);
        }
        _unsupportedOperationOrNullDereference(operation) {
            if (Null._operationIsSupported(this.getType(), operation)) {
                throw new Error(`${operation}() called on a null ${this.getType().name}.`);
            }
            throw new Error(`${operation}() is not supported by Ion type ${this.getType().name}`);
        }
        booleanValue() {
            return this._convertToJsNull("booleanValue");
        }
        numberValue() {
            return this._convertToJsNull("numberValue");
        }
        bigIntValue() {
            return this._convertToJsNull("bigIntValue");
        }
        decimalValue() {
            return this._convertToJsNull("decimalValue");
        }
        stringValue() {
            return this._convertToJsNull("stringValue");
        }
        dateValue() {
            return this._convertToJsNull("dateValue");
        }
        uInt8ArrayValue() {
            return this._convertToJsNull("uInt8ArrayValue");
        }
        fieldNames() {
            this._unsupportedOperationOrNullDereference("fieldNames");
        }
        fields() {
            this._unsupportedOperationOrNullDereference("fields");
        }
        elements() {
            this._unsupportedOperationOrNullDereference("elements");
        }
        get(...pathElements) {
            return null;
        }
        toString() {
            if (this.getType() == Ion_1.IonTypes.NULL) {
                return "null";
            }
            return "null." + this._ionType.name;
        }
        toJSON() {
            return null;
        }
        writeTo(writer) {
            writer.setAnnotations(this.getAnnotations());
            writer.writeNull(this.getType());
        }
        _valueEquals(other, options = {
            epsilon: null,
            ignoreAnnotations: false,
            ignoreTimestampPrecision: false,
            onlyCompareIon: true,
        }) {
            let isSupportedType = false;
            let valueToCompare = null;
            if (other instanceof Null) {
                isSupportedType = true;
                valueToCompare = other;
            }
            else if (!options.onlyCompareIon) {
                if (other === null && this._ionType.name === "null") {
                    return true;
                }
            }
            if (!isSupportedType) {
                return false;
            }
            return this._ionType.name === valueToCompare._ionType.name;
        }
    }
    exports.Null = Null;
    Null._supportedIonTypesByOperation = new Map([
        ["booleanValue", new Set([Ion_1.IonTypes.BOOL])],
        ["numberValue", new Set([Ion_1.IonTypes.INT, Ion_1.IonTypes.FLOAT, Ion_1.IonTypes.DECIMAL])],
        ["bigIntValue", new Set([Ion_1.IonTypes.INT])],
        ["decimalValue", new Set([Ion_1.IonTypes.DECIMAL])],
        ["stringValue", new Set([Ion_1.IonTypes.STRING, Ion_1.IonTypes.SYMBOL])],
        ["dateValue", new Set([Ion_1.IonTypes.TIMESTAMP])],
        ["timestampValue", new Set([Ion_1.IonTypes.TIMESTAMP])],
        ["uInt8ArrayValue", new Set([Ion_1.IonTypes.BLOB, Ion_1.IonTypes.CLOB])],
        ["fields", new Set([Ion_1.IonTypes.STRUCT])],
        ["fieldNames", new Set([Ion_1.IonTypes.STRUCT])],
        ["elements", new Set([Ion_1.IonTypes.LIST, Ion_1.IonTypes.SEXP, Ion_1.IonTypes.STRUCT])],
    ]);
});
//# sourceMappingURL=Null.js.map