define(["require", "exports", "../Ion", "./Sequence"], function (require, exports, Ion_1, Sequence_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.List = void 0;
    class List extends Sequence_1.Sequence(Ion_1.IonTypes.LIST) {
        constructor(children, annotations = []) {
            super(children, annotations);
        }
    }
    exports.List = List;
});
//# sourceMappingURL=List.js.map