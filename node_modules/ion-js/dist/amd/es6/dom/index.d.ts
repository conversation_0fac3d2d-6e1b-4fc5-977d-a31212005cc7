import { Reader, ReaderBuffer } from "../Ion";
import { Value } from "./Value";
export declare function loadAll(ionData: ReaderBuffer | Reader): Value[];
export declare function load(ionData: ReaderBuffer | Reader): Value | null;
export { Value, PathElement } from "./Value";
export { Null } from "./Null";
export { Boolean } from "./Boolean";
export { Integer } from "./Integer";
export { Float } from "./Float";
export { Decimal } from "./Decimal";
export { Timestamp } from "./Timestamp";
export { String } from "./String";
export { Symbol } from "./Symbol";
export { Blob } from "./Blob";
export { Clob } from "./Clob";
export { Struct } from "./Struct";
export { List } from "./List";
export { SExpression } from "./SExpression";
