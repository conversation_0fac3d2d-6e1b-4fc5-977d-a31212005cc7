import { Decimal, IonType, Writer } from "../Ion";
import { PathElement, Value } from "./Value";
declare const Null_base: {
    new (...args: any[]): {
        _ionType: IonType;
        _ionAnnotations: string[];
        _unsupportedOperation<T extends Value>(functionName: string): never;
        getType(): IonType;
        _setAnnotations(annotations: string[]): void;
        getAnnotations(): string[];
        isNull(): boolean;
        booleanValue(): boolean | null;
        numberValue(): number | null;
        bigIntValue(): bigint | null;
        decimalValue(): Decimal | null;
        stringValue(): string | null;
        dateValue(): Date | null;
        timestampValue(): import("../IonTimestamp").Timestamp | null;
        uInt8ArrayValue(): Uint8Array | null;
        fieldNames(): string[];
        fields(): [string, Value][];
        allFields(): [string, Value[]][];
        elements(): Value[];
        get(...pathElements: (string | number)[]): Value | null;
        getAll(...pathElements: (string | number)[]): Value[] | null;
        as<T_1 extends Value>(ionValueType: import("./Value").Constructor<T_1>): T_1;
        writeTo(writer: Writer): void;
        deleteField(name: string): boolean;
        _valueEquals(other: any, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
            onlyCompareIon?: boolean | undefined;
            coerceNumericType: boolean;
        }): boolean;
        equals(other: any, options?: {
            epsilon?: number | null | undefined;
        }): boolean;
        ionEquals(other: Value, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
        }): boolean;
    };
    _getIonType(): IonType;
    _fromJsValue(jsValue: any, annotations: string[]): Value;
} & ObjectConstructor;
export declare class Null extends Null_base {
    private static _supportedIonTypesByOperation;
    private static _operationIsSupported;
    constructor(ionType?: IonType, annotations?: string[]);
    isNull(): boolean;
    private _convertToJsNull;
    private _unsupportedOperationOrNullDereference;
    booleanValue(): boolean | null;
    numberValue(): number | null;
    bigIntValue(): bigint | null;
    decimalValue(): Decimal | null;
    stringValue(): string | null;
    dateValue(): Date | null;
    uInt8ArrayValue(): Uint8Array | null;
    fieldNames(): string[];
    fields(): [string, Value][];
    elements(): Value[];
    get(...pathElements: PathElement[]): Value | null;
    toString(): string;
    toJSON(): null;
    writeTo(writer: Writer): void;
    _valueEquals(other: any, options?: {
        epsilon?: number | null;
        ignoreAnnotations?: boolean;
        ignoreTimestampPrecision?: boolean;
        onlyCompareIon?: boolean;
    }): boolean;
}
export {};
