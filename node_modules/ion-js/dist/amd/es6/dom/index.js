var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    Object.defineProperty(o, k2, { enumerable: true, get: function() { return m[k]; } });
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
define(["require", "exports", "../Ion", "../Ion", "../IonBinaryReader", "../IonTextReader", "./Blob", "./Clob", "./Decimal", "./Float", "./Integer", "./List", "./Null", "./SExpression", "./Struct", "./Symbol", "./Timestamp", "./Value", "./Null", "./Boolean", "./Integer", "./Float", "./Decimal", "./Timestamp", "./String", "./Symbol", "./Blob", "./Clob", "./Struct", "./List", "./SExpression"], function (require, exports, ion, Ion_1, IonBinaryReader_1, IonTextReader_1, Blob_1, Clob_1, Decimal_1, Float_1, Integer_1, List_1, Null_1, SExpression_1, Struct_1, Symbol_1, Timestamp_1, Value_1, Null_2, Boolean_1, Integer_2, Float_2, Decimal_2, Timestamp_2, String_1, Symbol_2, Blob_2, Clob_2, Struct_2, List_2, SExpression_2) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.load = exports.loadAll = void 0;
    ion = __importStar(ion);
    function loadAll(ionData) {
        const reader = _createReader(ionData);
        const ionValues = [];
        while (reader.next()) {
            ionValues.push(_loadValue(reader));
        }
        return ionValues;
    }
    exports.loadAll = loadAll;
    function load(ionData) {
        const reader = _createReader(ionData);
        if (reader.type() === null) {
            reader.next();
        }
        return reader.type() === null ? null : _loadValue(reader);
    }
    exports.load = load;
    function _createReader(ionData) {
        if (ionData instanceof IonTextReader_1.TextReader || ionData instanceof IonBinaryReader_1.BinaryReader) {
            return ionData;
        }
        return Ion_1.makeReader(ionData);
    }
    function _loadValue(reader) {
        const ionType = reader.type();
        if (ionType === null) {
            throw new Error("loadValue() called when no further values were available to read.");
        }
        const annotations = reader.annotations();
        if (reader.isNull()) {
            return new Null_1.Null(reader.type(), annotations);
        }
        switch (ionType) {
            case Ion_1.IonTypes.NULL:
                return new Null_1.Null(Ion_1.IonTypes.NULL, annotations);
            case Ion_1.IonTypes.BOOL:
                return new ion.dom.Boolean(reader.booleanValue(), annotations);
            case Ion_1.IonTypes.INT:
                return reader.intSize() == Ion_1.IntSize.Number
                    ? new Integer_1.Integer(reader.numberValue(), annotations)
                    : new Integer_1.Integer(reader.bigIntValue(), annotations);
            case Ion_1.IonTypes.FLOAT:
                return new Float_1.Float(reader.numberValue(), annotations);
            case Ion_1.IonTypes.DECIMAL:
                return new Decimal_1.Decimal(reader.decimalValue(), annotations);
            case Ion_1.IonTypes.TIMESTAMP:
                return new Timestamp_1.Timestamp(reader.timestampValue(), annotations);
            case Ion_1.IonTypes.SYMBOL:
                return new Symbol_1.Symbol(reader.stringValue(), annotations);
            case Ion_1.IonTypes.STRING:
                return new ion.dom.String(reader.stringValue(), annotations);
            case Ion_1.IonTypes.CLOB:
                return new Clob_1.Clob(reader.uInt8ArrayValue(), annotations);
            case Ion_1.IonTypes.BLOB:
                return new Blob_1.Blob(reader.uInt8ArrayValue(), annotations);
            case Ion_1.IonTypes.LIST:
                return _loadList(reader);
            case Ion_1.IonTypes.SEXP:
                return _loadSExpression(reader);
            case Ion_1.IonTypes.STRUCT:
                return _loadStruct(reader);
            default:
                throw new Error(`Unrecognized IonType '${ionType}' found.`);
        }
    }
    function _loadStruct(reader) {
        const children = new Map();
        const annotations = reader.annotations();
        reader.stepIn();
        while (reader.next()) {
            if (children.has(reader.fieldName())) {
                children.get(reader.fieldName()).push(_loadValue(reader));
            }
            else {
                children.set(reader.fieldName(), [_loadValue(reader)]);
            }
        }
        reader.stepOut();
        return new Struct_1.Struct(children.entries(), annotations);
    }
    function _loadList(reader) {
        const annotations = reader.annotations();
        return new List_1.List(_loadSequence(reader), annotations);
    }
    function _loadSExpression(reader) {
        const annotations = reader.annotations();
        return new SExpression_1.SExpression(_loadSequence(reader), annotations);
    }
    function _loadSequence(reader) {
        const children = [];
        reader.stepIn();
        while (reader.next()) {
            children.push(_loadValue(reader));
        }
        reader.stepOut();
        return children;
    }
    Object.defineProperty(exports, "Value", { enumerable: true, get: function () { return Value_1.Value; } });
    Object.defineProperty(exports, "Null", { enumerable: true, get: function () { return Null_2.Null; } });
    Object.defineProperty(exports, "Boolean", { enumerable: true, get: function () { return Boolean_1.Boolean; } });
    Object.defineProperty(exports, "Integer", { enumerable: true, get: function () { return Integer_2.Integer; } });
    Object.defineProperty(exports, "Float", { enumerable: true, get: function () { return Float_2.Float; } });
    Object.defineProperty(exports, "Decimal", { enumerable: true, get: function () { return Decimal_2.Decimal; } });
    Object.defineProperty(exports, "Timestamp", { enumerable: true, get: function () { return Timestamp_2.Timestamp; } });
    Object.defineProperty(exports, "String", { enumerable: true, get: function () { return String_1.String; } });
    Object.defineProperty(exports, "Symbol", { enumerable: true, get: function () { return Symbol_2.Symbol; } });
    Object.defineProperty(exports, "Blob", { enumerable: true, get: function () { return Blob_2.Blob; } });
    Object.defineProperty(exports, "Clob", { enumerable: true, get: function () { return Clob_2.Clob; } });
    Object.defineProperty(exports, "Struct", { enumerable: true, get: function () { return Struct_2.Struct; } });
    Object.defineProperty(exports, "List", { enumerable: true, get: function () { return List_2.List; } });
    Object.defineProperty(exports, "SExpression", { enumerable: true, get: function () { return SExpression_2.SExpression; } });
});
//# sourceMappingURL=index.js.map