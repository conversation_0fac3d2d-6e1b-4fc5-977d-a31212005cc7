import { Writer } from "../Ion";
declare const Blob_base: {
    new (data: Uint8Array, annotations?: string[]): {
        [index: number]: number;
        uInt8ArrayValue(): Uint8Array;
        _valueEquals(other: any, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
            onlyCompareIon?: boolean | undefined;
        }): boolean;
        _ionType: import("../IonType").IonType;
        _ionAnnotations: string[];
        _unsupportedOperation<T extends import("./Value").Value>(functionName: string): never;
        getType(): import("../IonType").IonType;
        _setAnnotations(annotations: string[]): void;
        getAnnotations(): string[];
        isNull(): boolean;
        booleanValue(): boolean | null;
        numberValue(): number | null;
        bigIntValue(): bigint | null;
        decimalValue(): import("../IonDecimal").Decimal | null;
        stringValue(): string | null;
        dateValue(): Date | null;
        timestampValue(): import("../IonTimestamp").Timestamp | null;
        fieldNames(): string[];
        fields(): [string, import("./Value").Value][];
        allFields(): [string, import("./Value").Value[]][];
        elements(): import("./Value").Value[];
        get(...pathElements: (string | number)[]): import("./Value").Value | null;
        getAll(...pathElements: (string | number)[]): import("./Value").Value[] | null;
        as<T_1 extends import("./Value").Value>(ionValueType: import("./Value").Constructor<T_1>): T_1;
        writeTo(writer: Writer): void;
        deleteField(name: string): boolean;
        equals(other: any, options?: {
            epsilon?: number | null | undefined;
        }): boolean;
        ionEquals(other: import("./Value").Value, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
        }): boolean;
        readonly BYTES_PER_ELEMENT: number;
        readonly buffer: ArrayBufferLike;
        readonly byteLength: number;
        readonly byteOffset: number;
        copyWithin(target: number, start: number, end?: number | undefined): any;
        every(callbackfn: (value: number, index: number, array: Uint8Array) => unknown, thisArg?: any): boolean;
        fill(value: number, start?: number | undefined, end?: number | undefined): any;
        filter(callbackfn: (value: number, index: number, array: Uint8Array) => any, thisArg?: any): Uint8Array;
        find(predicate: (value: number, index: number, obj: Uint8Array) => boolean, thisArg?: any): number | undefined;
        findIndex(predicate: (value: number, index: number, obj: Uint8Array) => boolean, thisArg?: any): number;
        forEach(callbackfn: (value: number, index: number, array: Uint8Array) => void, thisArg?: any): void;
        indexOf(searchElement: number, fromIndex?: number | undefined): number;
        join(separator?: string | undefined): string;
        lastIndexOf(searchElement: number, fromIndex?: number | undefined): number;
        readonly length: number;
        map(callbackfn: (value: number, index: number, array: Uint8Array) => number, thisArg?: any): Uint8Array;
        reduce(callbackfn: (previousValue: number, currentValue: number, currentIndex: number, array: Uint8Array) => number): number;
        reduce(callbackfn: (previousValue: number, currentValue: number, currentIndex: number, array: Uint8Array) => number, initialValue: number): number;
        reduce<U>(callbackfn: (previousValue: U, currentValue: number, currentIndex: number, array: Uint8Array) => U, initialValue: U): U;
        reduceRight(callbackfn: (previousValue: number, currentValue: number, currentIndex: number, array: Uint8Array) => number): number;
        reduceRight(callbackfn: (previousValue: number, currentValue: number, currentIndex: number, array: Uint8Array) => number, initialValue: number): number;
        reduceRight<U_1>(callbackfn: (previousValue: U_1, currentValue: number, currentIndex: number, array: Uint8Array) => U_1, initialValue: U_1): U_1;
        reverse(): Uint8Array;
        set(array: ArrayLike<number>, offset?: number | undefined): void;
        slice(start?: number | undefined, end?: number | undefined): Uint8Array;
        some(callbackfn: (value: number, index: number, array: Uint8Array) => unknown, thisArg?: any): boolean;
        sort(compareFn?: ((a: number, b: number) => number) | undefined): any;
        subarray(begin?: number | undefined, end?: number | undefined): Uint8Array;
        toLocaleString: (() => string) & (() => string);
        toString: (() => string) & (() => string);
        valueOf: (() => Object) & (() => Uint8Array);
        [Symbol.iterator](): IterableIterator<number>;
        entries(): IterableIterator<[number, number]>;
        keys(): IterableIterator<number>;
        values(): IterableIterator<number>;
        readonly [Symbol.toStringTag]: "Uint8Array";
        includes(searchElement: number, fromIndex?: number | undefined): boolean;
    };
    _getIonType(): import("../IonType").IonType;
    _fromJsValue(jsValue: any, annotations: string[]): import("./Value").Value;
    readonly BYTES_PER_ELEMENT: number;
    of(...items: number[]): Uint8Array;
    from(arrayLike: ArrayLike<number>): Uint8Array;
    from<T_2>(arrayLike: ArrayLike<T_2>, mapfn: (v: T_2, k: number) => number, thisArg?: any): Uint8Array;
    from(arrayLike: Iterable<number>, mapfn?: ((v: number, k: number) => number) | undefined, thisArg?: any): Uint8Array;
};
export declare class Blob extends Blob_base {
    constructor(data: Uint8Array, annotations?: string[]);
    toJSON(): string;
    writeTo(writer: Writer): void;
}
export {};
