import * as ion from "../Ion";
import { Writer } from "../Ion";
declare const Timestamp_base: {
    new (...args: any[]): {
        _ionType: ion.IonType;
        _ionAnnotations: string[];
        _unsupportedOperation<T extends ion.dom.Value>(functionName: string): never;
        getType(): ion.IonType;
        _setAnnotations(annotations: string[]): void;
        getAnnotations(): string[];
        isNull(): boolean;
        booleanValue(): boolean | null;
        numberValue(): number | null;
        bigIntValue(): bigint | null;
        decimalValue(): ion.Decimal | null;
        stringValue(): string | null;
        dateValue(): Date | null;
        timestampValue(): ion.Timestamp | null;
        uInt8ArrayValue(): Uint8Array | null;
        fieldNames(): string[];
        fields(): [string, ion.dom.Value][];
        allFields(): [string, ion.dom.Value[]][];
        elements(): ion.dom.Value[];
        get(...pathElements: (string | number)[]): ion.dom.Value | null;
        getAll(...pathElements: (string | number)[]): ion.dom.Value[] | null;
        as<T_1 extends ion.dom.Value>(ionValueType: import("./Value").Constructor<T_1>): T_1;
        writeTo(writer: ion.Writer): void;
        deleteField(name: string): boolean;
        _valueEquals(other: any, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
            onlyCompareIon?: boolean | undefined;
            coerceNumericType: boolean;
        }): boolean;
        equals(other: any, options?: {
            epsilon?: number | null | undefined;
        }): boolean;
        ionEquals(other: ion.dom.Value, options?: {
            epsilon?: number | null | undefined;
            ignoreAnnotations?: boolean | undefined;
            ignoreTimestampPrecision?: boolean | undefined;
        }): boolean;
    };
    _getIonType(): ion.IonType;
    _fromJsValue(jsValue: any, annotations: string[]): ion.dom.Value;
} & DateConstructor;
export declare class Timestamp extends Timestamp_base {
    protected _timestamp: ion.Timestamp;
    protected _date: Date;
    constructor(dateOrTimestamp: Date | ion.Timestamp, annotations?: string[]);
    private static _timestampFromDate;
    timestampValue(): ion.Timestamp;
    dateValue(): Date;
    writeTo(writer: Writer): void;
    _valueEquals(other: any, options?: {
        epsilon?: number | null;
        ignoreAnnotations?: boolean;
        ignoreTimestampPrecision?: boolean;
        onlyCompareIon?: boolean;
    }): boolean;
}
export {};
