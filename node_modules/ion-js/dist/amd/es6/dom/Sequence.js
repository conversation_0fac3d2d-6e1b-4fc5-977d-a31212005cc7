define(["require", "exports", "../Ion", "./FromJsConstructor", "./Value"], function (require, exports, Ion_1, FromJsConstructor_1, Value_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Sequence = void 0;
    function Sequence(ionType) {
        return class extends Value_1.Value(Array, ionType, FromJsConstructor_1.FromJsConstructor.NONE) {
            constructor(children, annotations = []) {
                super();
                for (const child of children) {
                    this.push(child);
                }
                this._setAnnotations(annotations);
                return new Proxy(this, {
                    set: function (target, index, value) {
                        if (!(value instanceof Value_1.Value)) {
                            value = Value_1.Value.from(value);
                        }
                        target[index] = value;
                        return true;
                    },
                });
            }
            get(...pathElements) {
                if (pathElements.length === 0) {
                    throw new Error("Value#get requires at least one parameter.");
                }
                const [pathHead, ...pathTail] = pathElements;
                if (typeof pathHead !== "number") {
                    throw new Error(`Cannot index into a ${this.getType().name} with a ${typeof pathHead}.`);
                }
                const children = this;
                const maybeChild = children[pathHead];
                const child = maybeChild === undefined ? null : maybeChild;
                if (pathTail.length === 0 || child === null) {
                    return child;
                }
                return child.get(...pathTail);
            }
            elements() {
                return Object.values(this);
            }
            toString() {
                return "[" + this.join(", ") + "]";
            }
            static _fromJsValue(jsValue, annotations) {
                if (!(jsValue instanceof Array)) {
                    throw new Error(`Cannot create a ${this.name} from: ${jsValue.toString()}`);
                }
                const children = jsValue.map((child) => Value_1.Value.from(child));
                return new this(children, annotations);
            }
            writeTo(writer) {
                writer.setAnnotations(this.getAnnotations());
                writer.stepIn(ionType);
                for (const child of this) {
                    child.writeTo(writer);
                }
                writer.stepOut();
            }
            _valueEquals(other, options = {
                epsilon: null,
                ignoreAnnotations: false,
                ignoreTimestampPrecision: false,
                onlyCompareIon: true,
            }) {
                let isSupportedType = false;
                let valueToCompare = null;
                if (options.onlyCompareIon) {
                    if (other.getType() === Ion_1.IonTypes.LIST ||
                        other.getType() === Ion_1.IonTypes.SEXP) {
                        isSupportedType = true;
                        valueToCompare = other.elements();
                    }
                }
                else {
                    if (other instanceof Array) {
                        isSupportedType = true;
                        valueToCompare = other;
                    }
                }
                if (!isSupportedType) {
                    return false;
                }
                let actualSequence = this.elements();
                let expectedSequence = valueToCompare;
                if (actualSequence.length !== expectedSequence.length) {
                    return false;
                }
                for (let i = 0; i < actualSequence.length; i++) {
                    if (options.onlyCompareIon) {
                        if (!actualSequence[i].ionEquals(expectedSequence[i], options)) {
                            return false;
                        }
                    }
                    else {
                        if (!actualSequence[i].equals(expectedSequence[i])) {
                            return false;
                        }
                    }
                }
                return true;
            }
        };
    }
    exports.Sequence = Sequence;
});
//# sourceMappingURL=Sequence.js.map