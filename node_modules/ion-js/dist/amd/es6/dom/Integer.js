define(["require", "exports", "../Ion", "./FromJsConstructor", "./Value"], function (require, exports, Ion_1, FromJsConstructor_1, Value_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Integer = void 0;
    const _bigintConstructor = BigInt;
    const _fromJsConstructor = new FromJsConstructor_1.FromJsConstructorBuilder()
        .withPrimitives(FromJsConstructor_1.Primitives.Number, FromJsConstructor_1.Primitives.BigInt)
        .withClassesToUnbox(Number)
        .withClasses(_bigintConstructor)
        .build();
    class Integer extends Value_1.Value(Number, Ion_1.IonTypes.INT, _fromJsConstructor) {
        constructor(value, annotations = []) {
            if (typeof value === "number") {
                super(value);
                this._numberValue = value;
                this._bigIntValue = null;
            }
            else {
                const numberValue = Number(value);
                super(numberValue);
                this._bigIntValue = value;
                this._numberValue = numberValue;
            }
            this._setAnnotations(annotations);
        }
        bigIntValue() {
            if (this._bigIntValue === null) {
                this._bigIntValue = BigInt(this.numberValue());
            }
            return this._bigIntValue;
        }
        numberValue() {
            return this._numberValue;
        }
        toString() {
            if (this._bigIntValue === null) {
                return this._numberValue.toString();
            }
            return this._bigIntValue.toString();
        }
        valueOf() {
            return this.numberValue();
        }
        writeTo(writer) {
            writer.setAnnotations(this.getAnnotations());
            if (this._bigIntValue === null) {
                writer.writeInt(this.numberValue());
            }
            else {
                writer.writeInt(this._bigIntValue);
            }
        }
        _valueEquals(other, options = {
            epsilon: null,
            ignoreAnnotations: false,
            ignoreTimestampPrecision: false,
            onlyCompareIon: true,
        }) {
            let isSupportedType = false;
            let valueToCompare = null;
            if (other instanceof Integer) {
                isSupportedType = true;
                if (this._bigIntValue == null && other._bigIntValue == null) {
                    valueToCompare = other.numberValue();
                }
                else {
                    valueToCompare = other.bigIntValue();
                }
            }
            else if (!options.onlyCompareIon) {
                if (other instanceof Number || typeof other === "number") {
                    isSupportedType = true;
                    if (this.bigIntValue == null) {
                        valueToCompare = other.valueOf();
                    }
                    else {
                        valueToCompare = BigInt(other.valueOf());
                    }
                }
                else if (typeof other === "bigint") {
                    isSupportedType = true;
                    valueToCompare = other;
                }
            }
            if (!isSupportedType) {
                return false;
            }
            if (typeof valueToCompare === "bigint") {
                return this.bigIntValue() === valueToCompare;
            }
            return this.numberValue() == valueToCompare;
        }
    }
    exports.Integer = Integer;
});
//# sourceMappingURL=Integer.js.map