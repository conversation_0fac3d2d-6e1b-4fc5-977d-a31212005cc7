define(["require", "exports", "../Ion", "./Sequence"], function (require, exports, Ion_1, Sequence_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.SExpression = void 0;
    class SExpression extends Sequence_1.Sequence(Ion_1.IonTypes.SEXP) {
        constructor(children, annotations = []) {
            super(children, annotations);
        }
        toString() {
            return "(" + this.join(" ") + ")";
        }
    }
    exports.SExpression = SExpression;
});
//# sourceMappingURL=SExpression.js.map