/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { SharedSymbolTable } from "./IonSharedSymbolTable";
export declare class Import {
    private readonly _offset;
    private readonly _length;
    private readonly _parent;
    private readonly _symbolTable;
    constructor(parent: Import | null, symbolTable: SharedSymbolTable, length?: number | null);
    get parent(): Import | null;
    get offset(): number;
    get length(): number;
    get symbolTable(): SharedSymbolTable;
    getSymbolText(symbolId: number): string | undefined;
    getSymbolId(symbolText: string): number | undefined;
}
