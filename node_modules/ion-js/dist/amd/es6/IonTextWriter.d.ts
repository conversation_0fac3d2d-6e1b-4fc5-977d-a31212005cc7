/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { AbstractWriter } from "./AbstractWriter";
import { Decimal } from "./IonDecimal";
import { Timestamp } from "./IonTimestamp";
import { IonType } from "./IonType";
import { Writeable } from "./IonWriteable";
declare type Serializer<T> = (value: T) => void;
export declare enum State {
    VALUE = 0,
    STRUCT_FIELD = 1
}
export declare class Context {
    state: State;
    clean: boolean;
    containerType: IonType | null;
    constructor(myType: IonType | null);
}
export declare class TextWriter extends AbstractWriter {
    protected readonly writeable: Writeable;
    protected containerContext: Context[];
    constructor(writeable: Writeable);
    get isTopLevel(): boolean;
    protected get currentContainer(): Context;
    private static _serializeFloat;
    getBytes(): Uint8Array;
    writeBlob(value: Uint8Array): void;
    writeBoolean(value: boolean): void;
    writeClob(value: Uint8Array): void;
    writeDecimal(value: Decimal): void;
    protected _isInStruct(): boolean;
    writeFieldName(fieldName: string): void;
    writeFloat32(value: number): void;
    writeFloat64(value: number): void;
    writeInt(value: number | bigint): void;
    protected _writeNull(type: IonType): void;
    writeNull(type: IonType): void;
    writeString(value: string): void;
    writeSymbol(value: string): void;
    writeTimestamp(value: Timestamp): void;
    stepIn(type: IonType): void;
    stepOut(): void;
    close(): void;
    depth(): number;
    protected _serializeValue<T>(type: IonType, value: T, serialize: Serializer<T>): void;
    protected writeContainer(type: IonType, openingCharacter: number): void;
    protected handleSeparator(): void;
    protected writeUtf8(s: string): void;
    protected writeAnnotations(): void;
    protected _stepIn(container: IonType): void;
    protected writeSymbolToken(s: string): void;
    private _writeFloat;
    private readonly _floatSerializer;
    private isSid;
}
export {};
