/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
export declare const NIBBLE_MASK = 15;
export declare const BYTE_MASK = 255;
export declare const TYPE_SHIFT = 4;
export declare const BYTE_SHIFT = 8;
export declare const LEN_MASK = 15;
export declare const LEN_VAR = 14;
export declare const LEN_NULL = 15;
export declare const TB_NULL = 0;
export declare const TB_BOOL = 1;
export declare const TB_INT = 2;
export declare const TB_NEG_INT = 3;
export declare const TB_FLOAT = 4;
export declare const TB_DECIMAL = 5;
export declare const TB_TIMESTAMP = 6;
export declare const TB_SYMBOL = 7;
export declare const TB_STRING = 8;
export declare const TB_CLOB = 9;
export declare const TB_BLOB = 10;
export declare const TB_LIST = 11;
export declare const TB_SEXP = 12;
export declare const TB_STRUCT = 13;
export declare const TB_ANNOTATION = 14;
