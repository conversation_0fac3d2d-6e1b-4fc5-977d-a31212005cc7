/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import { IonType } from "./IonType";
export declare const IonTypes: {
    NULL: IonType;
    BOOL: IonType;
    INT: IonType;
    FLOAT: IonType;
    DECIMAL: IonType;
    TIMESTAMP: IonType;
    SYMBOL: IonType;
    STRING: IonType;
    CLOB: IonType;
    BLOB: IonType;
    LIST: IonType;
    SEXP: IonType;
    STRUCT: IonType;
};
