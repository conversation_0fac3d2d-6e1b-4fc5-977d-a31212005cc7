/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
define(["require", "exports", "./IonText", "./IonSymbolToken", "./IonTypes"], function (require, exports, IonText_1, IonSymbolToken_1, IonTypes_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.ParserTextRaw = exports.get_ion_type = void 0;
    const EOF = -1;
    const ERROR = -2;
    const T_NULL = 1;
    const T_BOOL = 2;
    const T_INT = 3;
    const T_HEXINT = 4;
    const T_FLOAT = 5;
    const T_FLOAT_SPECIAL = 6;
    const T_DECIMAL = 7;
    const T_TIMESTAMP = 8;
    const T_IDENTIFIER = 9;
    const T_OPERATOR = 10;
    const T_STRING1 = 11;
    const T_STRING2 = 12;
    const T_STRING3 = 13;
    const T_CLOB2 = 14;
    const T_CLOB3 = 15;
    const T_BLOB = 16;
    const T_SEXP = 17;
    const T_LIST = 18;
    const T_STRUCT = 19;
    const CH_CR = 13;
    const CH_NL = 10;
    const CH_BS = 92;
    const CH_FORWARD_SLASH = "/".charCodeAt(0);
    const CH_AS = 42;
    const CH_SQ = 39;
    const CH_DOUBLE_QUOTE = '"'.charCodeAt(0);
    const CH_CM = 44;
    const CH_OP = 40;
    const CH_CP = 41;
    const CH_LEFT_CURLY = "{".charCodeAt(0);
    const CH_CC = 125;
    const CH_OS = 91;
    const CH_CS = 93;
    const CH_CL = 58;
    const CH_DT = 46;
    const CH_EQ = 61;
    const CH_PS = 43;
    const CH_MS = 45;
    const CH_0 = 48;
    const CH_D = 68;
    const CH_E = 69;
    const CH_F = 70;
    const CH_T = 84;
    const CH_X = 88;
    const CH_Z = 90;
    const CH_d = 100;
    const CH_e = 101;
    const CH_f = 102;
    const CH_i = 105;
    const CH_n = 110;
    const CH_x = 120;
    const ESC_0 = 48;
    const ESC_a = 97;
    const ESC_b = 98;
    const ESC_t = 116;
    const ESC_nl = 110;
    const ESC_ff = 102;
    const ESC_cr = 114;
    const ESC_v = 118;
    const ESC_dq = CH_DOUBLE_QUOTE;
    const ESC_sq = CH_SQ;
    const ESC_qm = 63;
    const ESC_bs = 92;
    const ESC_fs = 47;
    const ESC_nl2 = 10;
    const ESC_nl3 = 13;
    const ESC_x = CH_x;
    const ESC_u = 117;
    const ESC_U = 85;
    const INF = [CH_i, CH_n, CH_f];
    const _UTF16_MASK = 0x03ff;
    function get_ion_type(t) {
        switch (t) {
            case EOF:
                return null;
            case ERROR:
                return null;
            case T_NULL:
                return IonTypes_1.IonTypes.NULL;
            case T_BOOL:
                return IonTypes_1.IonTypes.BOOL;
            case T_INT:
                return IonTypes_1.IonTypes.INT;
            case T_HEXINT:
                return IonTypes_1.IonTypes.INT;
            case T_FLOAT:
                return IonTypes_1.IonTypes.FLOAT;
            case T_FLOAT_SPECIAL:
                return IonTypes_1.IonTypes.FLOAT;
            case T_DECIMAL:
                return IonTypes_1.IonTypes.DECIMAL;
            case T_TIMESTAMP:
                return IonTypes_1.IonTypes.TIMESTAMP;
            case T_IDENTIFIER:
                return IonTypes_1.IonTypes.SYMBOL;
            case T_OPERATOR:
                return IonTypes_1.IonTypes.SYMBOL;
            case T_STRING1:
                return IonTypes_1.IonTypes.SYMBOL;
            case T_STRING2:
                return IonTypes_1.IonTypes.STRING;
            case T_STRING3:
                return IonTypes_1.IonTypes.STRING;
            case T_CLOB2:
                return IonTypes_1.IonTypes.CLOB;
            case T_CLOB3:
                return IonTypes_1.IonTypes.CLOB;
            case T_BLOB:
                return IonTypes_1.IonTypes.BLOB;
            case T_SEXP:
                return IonTypes_1.IonTypes.SEXP;
            case T_LIST:
                return IonTypes_1.IonTypes.LIST;
            case T_STRUCT:
                return IonTypes_1.IonTypes.STRUCT;
            default:
                throw new Error("Unknown type: " + String(t) + ".");
        }
    }
    exports.get_ion_type = get_ion_type;
    function get_keyword_type(str) {
        if (str === "null") {
            return T_NULL;
        }
        if (str === "true") {
            return T_BOOL;
        }
        if (str === "false") {
            return T_BOOL;
        }
        if (str === "nan") {
            return T_FLOAT_SPECIAL;
        }
        if (str === "+inf") {
            return T_FLOAT_SPECIAL;
        }
        if (str === "-inf") {
            return T_FLOAT_SPECIAL;
        }
        throw new Error("Unknown keyword: " + str + ".");
    }
    function get_type_from_name(str) {
        if (str === "null") {
            return T_NULL;
        }
        if (str === "bool") {
            return T_BOOL;
        }
        if (str === "int") {
            return T_INT;
        }
        if (str === "float") {
            return T_FLOAT;
        }
        if (str === "decimal") {
            return T_DECIMAL;
        }
        if (str === "timestamp") {
            return T_TIMESTAMP;
        }
        if (str === "symbol") {
            return T_IDENTIFIER;
        }
        if (str === "string") {
            return T_STRING2;
        }
        if (str === "clob") {
            return T_CLOB2;
        }
        if (str === "blob") {
            return T_BLOB;
        }
        if (str === "sexp") {
            return T_SEXP;
        }
        if (str === "list") {
            return T_LIST;
        }
        if (str === "struct") {
            return T_STRUCT;
        }
        throw new Error("Unknown type: " + str + ".");
    }
    function get_hex_value(ch) {
        switch (ch) {
            case 48:
                return 0;
            case 49:
                return 1;
            case 50:
                return 2;
            case 51:
                return 3;
            case 52:
                return 4;
            case 53:
                return 5;
            case 54:
                return 6;
            case 55:
                return 7;
            case 56:
                return 8;
            case 57:
                return 9;
            case 97:
                return 10;
            case 98:
                return 11;
            case 99:
                return 12;
            case 100:
                return 13;
            case 101:
                return 14;
            case 102:
                return 15;
            case 65:
                return 10;
            case 66:
                return 11;
            case 67:
                return 12;
            case 68:
                return 13;
            case 69:
                return 14;
            case 70:
                return 15;
        }
        throw new Error("Unexpected bad hex digit in checked data.");
    }
    function is_valid_base64_length(char_length, trailer_length) {
        if (trailer_length > 2) {
            return false;
        }
        if (((char_length + trailer_length) & 0x3) != 0) {
            return false;
        }
        return true;
    }
    function is_valid_string_char(ch, allow_new_line) {
        if (ch == CH_CR) {
            return allow_new_line;
        }
        if (ch == CH_NL) {
            return allow_new_line;
        }
        if (IonText_1.is_whitespace(ch)) {
            return true;
        }
        if (ch < 32) {
            return false;
        }
        return true;
    }
    class ParserTextRaw {
        constructor(source) {
            this._value_null = false;
            this._curr_null = false;
            this._read_value_helper_minus = function (ch1, accept_operator_symbols, calling_op) {
                let op, ch2 = this._peek();
                if (ch2 == CH_i) {
                    ch2 = this._peek("inf");
                    if (IonText_1.isNumericTerminator(ch2)) {
                        op = this._read_minus_inf;
                    }
                    else if (accept_operator_symbols) {
                        op = this._read_operator_symbol;
                    }
                }
                else if (IonText_1.is_digit(ch2)) {
                    op = this._read_number;
                }
                else if (accept_operator_symbols) {
                    op = this._read_operator_symbol;
                }
                if (op != undefined) {
                    this._ops.unshift(op);
                    this._unread(ch1);
                }
                else {
                    this._error("operator symbols are not valid outside of sexps");
                }
            };
            this._read_string_helper = function (terminator, allow_new_line) {
                let ch;
                this._start = this._in.position();
                for (;;) {
                    ch = this._read();
                    if (ch == CH_BS) {
                        this._read_string_escape_sequence();
                    }
                    else if (ch == terminator) {
                        break;
                    }
                    else if (!is_valid_string_char(ch, allow_new_line)) {
                        throw new Error("invalid character " + ch + " in string");
                    }
                }
            };
            this._in = source;
            this._ops = [this._read_datagram_values];
            this._value_type = ERROR;
            this._value = [];
            this._start = -1;
            this._end = -1;
            this._esc_len = -1;
            this._curr = EOF;
            this._ann = [];
            this._msg = "";
            this._fieldname = null;
            this._fieldnameType = null;
            const helpers = {
                40: this._read_value_helper_paren,
                91: this._read_value_helper_square,
                123: this._read_value_helper_curly,
                43: this._read_value_helper_plus,
                45: this._read_value_helper_minus,
                39: this._read_value_helper_single,
                34: this._read_value_helper_double,
            };
            const set_helper = function (str, fn) {
                let i = str.length, ch;
                while (i > 0) {
                    i--;
                    ch = str.charCodeAt(i);
                    helpers[ch] = fn;
                }
            };
            set_helper("0123456789", this._read_value_helper_digit);
            set_helper("_$abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", this._read_value_helper_letter);
            set_helper("!#%&*+-./;<=>?@^`|~", this._read_value_helper_operator);
            helpers[CH_PS] = this._read_value_helper_plus;
            helpers[CH_MS] = this._read_value_helper_minus;
            this._read_value_helper_helpers = helpers;
        }
        fieldName() {
            return this._fieldname;
        }
        fieldNameType() {
            return this._fieldnameType;
        }
        source() {
            return this._in;
        }
        annotations() {
            return this._ann;
        }
        clearFieldName() {
            this._fieldname = null;
            this._fieldnameType = null;
        }
        isNull() {
            return this._curr_null;
        }
        bigIntValue() {
            if (this.isNull()) {
                return null;
            }
            const intText = this.get_value_as_string(this._curr).toLowerCase();
            switch (this._curr) {
                case T_INT:
                case T_HEXINT:
                    if (intText.startsWith("-")) {
                        const i = BigInt(intText.slice(1));
                        return -i;
                    }
                    return BigInt(intText);
                default:
                    throw new Error("intValue() was called when the current value was not an integer.");
            }
        }
        numberValue() {
            if (this.isNull()) {
                return null;
            }
            const s = this.get_value_as_string(this._curr);
            switch (this._curr) {
                case T_INT:
                case T_HEXINT:
                    return Number(BigInt(s));
                case T_FLOAT:
                    return Number(s);
                case T_FLOAT_SPECIAL:
                    if (s == "+inf") {
                        return Number.POSITIVE_INFINITY;
                    }
                    else if (s == "-inf") {
                        return Number.NEGATIVE_INFINITY;
                    }
                    else if (s == "nan") {
                        return Number.NaN;
                    }
                default:
                    throw new Error("can't convert to number");
            }
        }
        booleanValue() {
            if (this.isNull()) {
                return null;
            }
            const s = this.get_value_as_string(T_BOOL);
            if (s === "true") {
                return true;
            }
            else if (s === "false") {
                return false;
            }
            throw new Error("Unrecognized Boolean value '" + s + "'");
        }
        get_value_as_string(t) {
            let index;
            let ch;
            let s = "";
            switch (t) {
                case T_NULL:
                case T_BOOL:
                case T_INT:
                case T_HEXINT:
                case T_FLOAT:
                case T_FLOAT_SPECIAL:
                case T_DECIMAL:
                case T_TIMESTAMP:
                case T_IDENTIFIER:
                case T_OPERATOR:
                    for (index = this._start; index < this._end; index++) {
                        s += String.fromCharCode(this._in.valueAt(index));
                    }
                    break;
                case T_BLOB:
                    for (index = this._start; index < this._end; index++) {
                        ch = this._in.valueAt(index);
                        if (IonText_1.is_base64_char(ch)) {
                            s += String.fromCharCode(ch);
                        }
                    }
                    break;
                case T_STRING1:
                case T_STRING2:
                case T_STRING3:
                    for (index = this._start; index < this._end; index++) {
                        let isEscaped = false;
                        ch = this._in.valueAt(index);
                        if (ch == CH_BS) {
                            ch = this._read_escape_sequence(index, this._end);
                            index += this._esc_len;
                            isEscaped = true;
                        }
                        if (this.isHighSurrogate(ch)) {
                            index++;
                            let tempChar = this._in.valueAt(index);
                            if (tempChar == CH_BS) {
                                tempChar = this._read_escape_sequence(index, this._end);
                                index += this._esc_len;
                            }
                            if (this.isLowSurrogate(tempChar)) {
                                const hiSurrogate = ch;
                                const loSurrogate = tempChar;
                                const codepoint = 0x10000 +
                                    ((hiSurrogate & _UTF16_MASK) << 10) +
                                    (loSurrogate & _UTF16_MASK);
                                s += String.fromCodePoint(codepoint);
                            }
                            else {
                                throw new Error("expected a low surrogate, but found: " + ch);
                            }
                        }
                        else if (this.isLowSurrogate(ch)) {
                            throw new Error("unexpected low surrogate: " + ch);
                        }
                        else if (t === T_STRING3 &&
                            ch === CH_SQ &&
                            !isEscaped &&
                            this.verifyTriple(index)) {
                            index = this._skip_triple_quote_gap(index, this._end, true);
                        }
                        else if (ch >= 0) {
                            if (isEscaped) {
                                s += String.fromCodePoint(ch);
                            }
                            else {
                                if (t === T_STRING3 &&
                                    ch === ESC_nl3 &&
                                    this._in.valueAt(index + 1) === ESC_nl2) {
                                    ch = ESC_nl2;
                                    index++;
                                }
                                s += String.fromCharCode(ch);
                            }
                        }
                    }
                    break;
                default:
                    throw new Error("can't get this value as a string");
            }
            return s;
        }
        get_value_as_uint8array(t) {
            const bytes = [];
            switch (t) {
                case T_CLOB2:
                    for (let index = this._start; index < this._end; index++) {
                        const ch = this._in.valueAt(index);
                        if (ch === CH_BS) {
                            bytes.push(this.readClobEscapes(index, this._end));
                            index += this._esc_len;
                        }
                        else if (ch < 128) {
                            bytes.push(ch);
                        }
                        else {
                            throw new Error("Non-Ascii values illegal within clob.");
                        }
                    }
                    break;
                case T_CLOB3:
                    for (let index = this._start; index < this._end; index++) {
                        const ch = this._in.valueAt(index);
                        if (ch === CH_BS) {
                            const escaped = this.readClobEscapes(index, this._end);
                            if (escaped >= 0) {
                                bytes.push(escaped);
                            }
                            index += this._esc_len;
                        }
                        else if (ch === CH_SQ) {
                            if (this.verifyTriple(index)) {
                                index = this._skip_triple_quote_gap(index, this._end, false);
                            }
                            else {
                                bytes.push(ch);
                            }
                        }
                        else if (ch < 128) {
                            bytes.push(ch);
                        }
                        else {
                            throw new Error("Non-Ascii values illegal within clob.");
                        }
                    }
                    break;
                default:
                    throw new Error("can't get this value as a Uint8Array");
            }
            return Uint8Array.from(bytes);
        }
        next() {
            this.clearFieldName();
            this._ann = [];
            if (this._value_type === ERROR) {
                this._run();
            }
            this._curr = this._value_pop();
            let t;
            if (this._curr === ERROR) {
                this._value.push(ERROR);
                return undefined;
            }
            else {
                t = this._curr;
            }
            this._curr_null = this._value_null;
            this._value_null = false;
            return t;
        }
        _read_datagram_values() {
            const ch = this._peek();
            if (ch == EOF) {
                this._value_push(EOF);
            }
            else {
                this._ops.unshift(this._read_datagram_values);
                this._ops.unshift(this._read_value);
            }
        }
        _read_sexp_values() {
            const ch = this._read_after_whitespace(true);
            if (ch == CH_CP) {
                this._value_push(EOF);
            }
            else if (ch === EOF) {
                throw new Error("Expected closing ).");
            }
            else {
                this._unread(ch);
                this._ops.unshift(this._read_sexp_values);
                this._ops.unshift(this._read_sexp_value);
            }
        }
        _read_list_values() {
            const ch = this._read_after_whitespace(true);
            if (ch == CH_CS) {
                this._value_push(EOF);
            }
            else {
                this._unread(ch);
                this._ops.unshift(this._read_list_comma);
                this._ops.unshift(this._read_value);
            }
        }
        _read_struct_values() {
            let op = this._done_with_error, ch = this._read_after_whitespace(true);
            switch (ch) {
                case CH_SQ:
                    op = this._read_string1;
                    if (this._peek("''") != ERROR) {
                        op = this._read_string3;
                    }
                    break;
                case CH_DOUBLE_QUOTE:
                    op = this._read_string2;
                    break;
                case CH_CC:
                    this._value_push(EOF);
                    return;
                default:
                    if (IonText_1.is_letter(ch)) {
                        op = this._read_symbol;
                    }
                    break;
            }
            if (op === this._done_with_error) {
                this._error("expected field name (or close struct '}') not found");
            }
            else {
                op.call(this);
                this._load_field_name();
                ch = this._read_after_whitespace(true);
                if (ch != CH_CL) {
                    this._error("expected ':'");
                    return;
                }
                this._ops.unshift(this._read_struct_comma);
                this._ops.unshift(this._read_value);
            }
        }
        _read_list_comma() {
            let ch = this._read_after_whitespace(true);
            if (ch == CH_CM) {
                ch = this._read_after_whitespace(true);
                if (ch == CH_CS) {
                    this._value_push(EOF);
                }
                else {
                    this._unread(ch);
                    this._ops.unshift(this._read_list_comma);
                    this._ops.unshift(this._read_value);
                }
            }
            else if (ch == CH_CS) {
                this._value_push(EOF);
            }
            else {
                this._error("expected ',' or ']'");
            }
        }
        _read_struct_comma() {
            let ch = this._read_after_whitespace(true);
            if (ch == CH_CM) {
                ch = this._read_after_whitespace(true);
                if (ch == CH_CC) {
                    this._value_push(EOF);
                }
                else {
                    this._unread(ch);
                    this._ops.unshift(this._read_struct_values);
                }
            }
            else if (ch == CH_CC) {
                this._value_push(EOF);
            }
            else {
                this._error("expected ',' or '}'");
            }
        }
        _load_field_name() {
            this._fieldnameType = this._value_pop();
            const s = this.get_value_as_string(this._fieldnameType);
            switch (this._fieldnameType) {
                case T_IDENTIFIER:
                    if (IonText_1.is_keyword(s)) {
                        throw new Error("can't use '" + s + "' as a fieldname without quotes");
                    }
                case T_STRING1:
                case T_STRING2:
                case T_STRING3:
                    this._fieldname = s;
                    break;
                default:
                    throw new Error("invalid fieldname" + s);
            }
        }
        _read_value() {
            this._read_value_helper(false, this._read_value);
        }
        _read_sexp_value() {
            this._read_value_helper(true, this._read_sexp_value);
        }
        _read_value_helper(accept_operator_symbols, calling_op) {
            const ch = this._read_after_whitespace(true);
            if (ch == EOF) {
                this._read_value_helper_EOF(ch, accept_operator_symbols, calling_op);
            }
            else {
                const fn = this._read_value_helper_helpers[ch];
                if (fn != undefined) {
                    fn.call(this, ch, accept_operator_symbols, calling_op);
                }
                else {
                    this._error("unexpected character '" + IonText_1.asAscii(ch) + "'");
                }
            }
        }
        _read_value_helper_EOF(ch1, accept_operator_symbols, calling_op) {
            this._ops.unshift(this._done);
        }
        _read_value_helper_paren(ch1, accept_operator_symbols, calling_op) {
            this._value_push(T_SEXP);
            this._ops.unshift(this._read_sexp_values);
        }
        _read_value_helper_square(ch1, accept_operator_symbols, calling_op) {
            this._value_push(T_LIST);
            this._ops.unshift(this._read_list_values);
        }
        _read_value_helper_curly(ch1, accept_operator_symbols, calling_op) {
            let ch3;
            const ch2 = this._read();
            if (ch2 == CH_LEFT_CURLY) {
                ch3 = this._read_after_whitespace(false);
                if (ch3 == CH_SQ) {
                    this._ops.unshift(this._read_clob_string3);
                }
                else if (ch3 == CH_DOUBLE_QUOTE) {
                    this._ops.unshift(this._read_clob_string2);
                }
                else {
                    this._unread(ch3);
                    this._ops.unshift(this._read_blob);
                }
            }
            else {
                this._unread(ch2);
                this._value_push(T_STRUCT);
                this._ops.unshift(this._read_struct_values);
            }
        }
        _read_value_helper_plus(ch1, accept_operator_symbols, calling_op) {
            const ch2 = this._peek("inf");
            this._unread(ch1);
            if (IonText_1.isNumericTerminator(ch2)) {
                this._ops.unshift(this._read_plus_inf);
            }
            else if (accept_operator_symbols) {
                this._ops.unshift(this._read_operator_symbol);
            }
            else {
                this._error("unexpected '+'");
            }
        }
        _read_value_helper_digit(ch1, accept_operator_symbols, calling_op) {
            const ch2 = this._peek_4_digits(ch1);
            this._unread(ch1);
            if (ch2 == CH_T || ch2 == CH_MS) {
                this._ops.unshift(this._readTimestamp);
            }
            else {
                this._ops.unshift(this._read_number);
            }
        }
        _read_value_helper_single(ch1, accept_operator_symbols, calling_op) {
            let op;
            if (this._peek("''") != ERROR) {
                op = this._read_string3;
                op.call(this);
            }
            else {
                op = this._read_string1;
                op.call(this);
                if (this._test_string_as_annotation(op)) {
                    this._ops.unshift(calling_op);
                }
            }
        }
        _read_value_helper_double(ch1, accept_operator_symbols, calling_op) {
            this._ops.unshift(this._read_string2);
        }
        _read_value_helper_letter(ch1, accept_operator_symbols, calling_op) {
            this._read_symbol();
            const type = this._value_pop();
            if (type != T_IDENTIFIER) {
                throw new Error("Expecting symbol here.");
            }
            let symbol = this.get_value_as_string(type);
            if (IonText_1.is_keyword(symbol)) {
                let kwt = get_keyword_type(symbol);
                if (kwt === T_NULL) {
                    this._value_null = true;
                    if (this._peek() === CH_DT) {
                        this._read();
                        const ch = this._read();
                        if (IonText_1.is_letter(ch) !== true) {
                            throw new Error("Expected type name after 'null.'");
                        }
                        this._read_symbol();
                        if (this._value_pop() !== T_IDENTIFIER) {
                            throw new Error("Expected type name after 'null.'");
                        }
                        symbol = this.get_value_as_string(T_IDENTIFIER);
                        kwt = get_type_from_name(symbol);
                    }
                    this._start = -1;
                    this._end = -1;
                }
                this._value_push(kwt);
            }
            else {
                const ch = this._read_after_whitespace(true);
                if (ch == CH_CL && this._peek() == CH_CL) {
                    this._read();
                    const sid = this._parseSymbolId(symbol);
                    if (sid === 0) {
                        throw new Error("Symbol ID zero is not supported.");
                    }
                    else if (isNaN(sid)) {
                        this._ann.push(new IonSymbolToken_1.SymbolToken(symbol));
                    }
                    else {
                        this._ann.push(new IonSymbolToken_1.SymbolToken(null, sid));
                    }
                    this._ops.unshift(calling_op);
                }
                else {
                    const kwt = T_IDENTIFIER;
                    this._unread(ch);
                    this._value_push(kwt);
                }
            }
        }
        _read_value_helper_operator(ch1, accept_operator_symbols, calling_op) {
            if (accept_operator_symbols) {
                this._unread(ch1);
                this._ops.unshift(this._read_operator_symbol);
            }
            else {
                this._error("unexpected operator character");
            }
        }
        _done() {
            this._value_push(EOF);
        }
        _done_with_error() {
            this._value_push(ERROR);
            throw new Error(this._error_msg);
        }
        _read_number() {
            let ch, t;
            this._start = this._in.position();
            ch = this._read();
            if (ch == CH_MS) {
                ch = this._read();
            }
            if (ch == CH_0) {
                ch = this._peek();
                if (ch == CH_x || ch == CH_X) {
                    this._read_hex_int();
                    return;
                }
                if (IonText_1.is_digit(ch)) {
                    this._error("leading zeros are not allowed");
                }
                ch = CH_0;
            }
            t = T_INT;
            ch = this._read_required_digits(ch);
            if (ch == CH_DT) {
                t = T_DECIMAL;
                ch = this._read_optional_digits(this._read());
            }
            if (!IonText_1.isNumericTerminator(ch)) {
                if (ch == CH_d || ch == CH_D) {
                    t = T_DECIMAL;
                    ch = this._read_exponent();
                }
                else if (ch == CH_e || ch == CH_E || ch == CH_f || ch == CH_F) {
                    t = T_FLOAT;
                    ch = this._read_exponent();
                }
            }
            if (!IonText_1.isNumericTerminator(ch)) {
                this._error("invalid character after number");
            }
            else {
                this._unread(ch);
                this._end = this._in.position();
                this._value_push(t);
            }
        }
        _read_hex_int() {
            let ch = this._read();
            if (ch == CH_x || ch == CH_X) {
                ch = this._read();
                ch = this._read_required_hex_digits(ch);
            }
            if (IonText_1.isNumericTerminator(ch)) {
                this._unread(ch);
                this._end = this._in.position();
                this._value_push(T_HEXINT);
            }
            else {
                this._error("invalid character after number");
            }
        }
        _read_exponent() {
            let ch = this._read();
            if (ch == CH_MS || ch == CH_PS) {
                ch = this._read();
            }
            ch = this._read_required_digits(ch);
            return ch;
        }
        _read_plus_inf() {
            this._start = this._in.position();
            if (this._read() == CH_PS) {
                this._read_inf_helper();
            }
            else {
                this._error("expected +inf");
            }
        }
        _read_minus_inf() {
            this._start = this._in.position();
            if (this._read() == CH_MS) {
                this._read_inf_helper();
            }
            else {
                this._error("expected -inf");
            }
        }
        _read_inf_helper() {
            let ii, ch;
            for (ii = 0; ii < 3; ii++) {
                ch = this._read();
                if (ch != INF[ii]) {
                    this._error("expected 'inf'");
                    return;
                }
            }
            if (IonText_1.isNumericTerminator(this._peek())) {
                this._end = this._in.position();
                this._value_push(T_FLOAT_SPECIAL);
            }
            else {
                this._error("invalid numeric terminator after 'inf'");
            }
        }
        _readTimestamp() {
            this._start = this._in.position();
            let ch = this._readPastNDigits(4);
            if (ch === CH_T) {
                this._end = this._in.position();
                this._value_push(T_TIMESTAMP);
                return;
            }
            else if (ch !== CH_MS) {
                throw new Error("Timestamp year must be followed by '-' or 'T'.");
            }
            ch = this._readPastNDigits(2);
            if (ch === CH_T) {
                this._end = this._in.position();
                this._value_push(T_TIMESTAMP);
                return;
            }
            else if (ch !== CH_MS) {
                throw new Error("Timestamp month must be followed by '-' or 'T'.");
            }
            ch = this._readPastNDigits(2);
            if (IonText_1.isNumericTerminator(ch)) {
                this._unread(ch);
                this._end = this._in.position();
                this._value_push(T_TIMESTAMP);
                return;
            }
            else if (ch !== CH_T) {
                throw new Error("Timestamp day must be followed by a numeric stop character .");
            }
            const peekChar = this._in.peek();
            if (IonText_1.isNumericTerminator(peekChar)) {
                this._end = this._in.position();
                this._value_push(T_TIMESTAMP);
                return;
            }
            else if (!IonText_1.is_digit(peekChar)) {
                throw new Error("Timestamp DATE must be followed by numeric terminator or additional TIME digits.");
            }
            ch = this._readPastNDigits(2);
            if (ch !== CH_CL) {
                throw new Error("Timestamp time(hr:min) requires format of 00:00");
            }
            ch = this._readPastNDigits(2);
            if (ch === CH_CL) {
                ch = this._readPastNDigits(2);
                if (ch === CH_DT) {
                    if (!IonText_1.is_digit(this._read())) {
                        throw new Error("W3C timestamp spec requires atleast one digit after decimal point.");
                    }
                    while (IonText_1.is_digit((ch = this._read()))) { }
                }
            }
            if (ch === CH_Z) {
                if (!IonText_1.isNumericTerminator(this._peek())) {
                    throw new Error("Illegal terminator after Zulu offset.");
                }
                this._end = this._in.position();
                this._value_push(T_TIMESTAMP);
                return;
            }
            else if (ch !== CH_PS && ch !== CH_MS) {
                throw new Error("Timestamps require an offset.");
            }
            ch = this._readPastNDigits(2);
            if (ch !== CH_CL) {
                throw new Error("Timestamp offset(hr:min) requires format of +/-00:00.");
            }
            this._readNDigits(2);
            ch = this._peek();
            if (!IonText_1.isNumericTerminator(ch)) {
                throw new Error("Improperly formatted timestamp.");
            }
            this._end = this._in.position();
            this._value_push(T_TIMESTAMP);
        }
        _read_symbol() {
            let ch;
            this._start = this._in.position() - 1;
            for (;;) {
                ch = this._read();
                if (!IonText_1.is_letter_or_digit(ch)) {
                    break;
                }
            }
            this._unread(ch);
            this._end = this._in.position();
            this._value_push(T_IDENTIFIER);
        }
        _read_operator_symbol() {
            let ch;
            this._start = this._in.position();
            for (;;) {
                ch = this._read();
                if (!IonText_1.is_operator_char(ch)) {
                    break;
                }
            }
            this._end = this._in.position() - 1;
            this._unread(ch);
            this._value_push(T_OPERATOR);
        }
        _read_string1() {
            this._read_string_helper(CH_SQ, false);
            this._end = this._in.position() - 1;
            this._value_push(T_STRING1);
        }
        _read_string2() {
            this._read_string_helper(CH_DOUBLE_QUOTE, false);
            this._end = this._in.position() - 1;
            this._value_push(T_STRING2);
        }
        _read_string3(recognizeComments) {
            if (recognizeComments === undefined) {
                recognizeComments = true;
            }
            let ch;
            this._unread(this._peek(""));
            for (this._start = this._in.position() + 3; this._peek("'''") !== ERROR; this._in.unread(this._read_after_whitespace(recognizeComments))) {
                for (let i = 0; i < 3; i++) {
                    this._read();
                }
                while (this._peek("'''") === ERROR) {
                    ch = this._read();
                    if (ch == CH_BS) {
                        this._read_string_escape_sequence();
                    }
                    if (ch === EOF) {
                        throw new Error("Closing triple quotes not found.");
                    }
                    if (!is_valid_string_char(ch, true)) {
                        throw new Error("invalid character " + ch + " in string");
                    }
                }
                this._end = this._in.position();
                for (let i = 0; i < 3; i++) {
                    this._read();
                }
            }
            this._value_push(T_STRING3);
        }
        verifyTriple(entryIndex) {
            return (this._in.valueAt(entryIndex) === CH_SQ &&
                this._in.valueAt(entryIndex + 1) === CH_SQ &&
                this._in.valueAt(entryIndex + 2) === CH_SQ);
        }
        _read_string_escape_sequence() {
            let ch = this._read();
            switch (ch) {
                case ESC_0:
                case ESC_a:
                case ESC_b:
                case ESC_t:
                case ESC_nl:
                case ESC_ff:
                case ESC_cr:
                case ESC_v:
                case ESC_dq:
                case ESC_sq:
                case ESC_qm:
                case ESC_bs:
                case ESC_fs:
                case ESC_nl2:
                    break;
                case ESC_nl3:
                    ch = this._read();
                    if (ch != ESC_nl2) {
                        this._unread(ch);
                    }
                    break;
                case ESC_x:
                    ch = this._read_N_hexdigits(2);
                    this._unread(ch);
                    break;
                case ESC_u:
                    ch = this._read_N_hexdigits(4);
                    this._unread(ch);
                    break;
                case ESC_U:
                    ch = this._read_N_hexdigits(8);
                    this._unread(ch);
                    break;
                default:
                    this._error("unexpected character: " + ch + " after escape slash");
            }
        }
        _test_string_as_annotation(op) {
            let s, ch, is_ann;
            const t = this._value_pop();
            if (t != T_STRING1 && t != T_STRING3) {
                this._error("expecting quoted symbol here");
            }
            s = this.get_value_as_string(t);
            ch = this._read_after_whitespace(true);
            if (ch == CH_CL && this._peek() == CH_CL) {
                this._read();
                this._ann.push(new IonSymbolToken_1.SymbolToken(s));
                is_ann = true;
            }
            else {
                this._unread(ch);
                this._value_push(t);
                is_ann = false;
            }
            return is_ann;
        }
        _read_clob_string2() {
            let t;
            this._read_string2();
            t = this._value_pop();
            if (t != T_STRING2) {
                this._error("string expected");
            }
            this._value_push(T_CLOB2);
            this._ops.unshift(this._read_close_double_brace);
        }
        _read_clob_string3() {
            let t;
            this._read_string3(false);
            t = this._value_pop();
            if (t != T_STRING3) {
                this._error("string expected");
            }
            this._value_push(T_CLOB3);
            this._ops.unshift(this._read_close_double_brace);
        }
        _read_blob() {
            let ch, base64_chars = 0, trailers = 0;
            this._start = this._in.position();
            while (true) {
                ch = this._read();
                if (IonText_1.is_base64_char(ch)) {
                    base64_chars++;
                    this._end = this._in.position();
                }
                else if (!IonText_1.is_whitespace(ch)) {
                    break;
                }
            }
            while (ch == CH_EQ) {
                trailers++;
                ch = this._read_after_whitespace(false);
            }
            if (ch != CH_CC || this._read() != CH_CC) {
                throw new Error("Invalid blob");
            }
            if (!is_valid_base64_length(base64_chars, trailers)) {
                throw new Error("Invalid base64 value");
            }
            this._value_push(T_BLOB);
        }
        _read_close_double_brace() {
            const ch = this._read_after_whitespace(false);
            if (ch != CH_CC || this._read() != CH_CC) {
                this._error("expected '}}'");
            }
        }
        isHighSurrogate(ch) {
            return ch >= 0xd800 && ch <= 0xdbff;
        }
        isLowSurrogate(ch) {
            return ch >= 0xdc00 && ch <= 0xdfff;
        }
        indexWhiteSpace(index, acceptComments) {
            let ch = this._in.valueAt(index);
            if (!acceptComments) {
                for (; IonText_1.is_whitespace(ch); ch = this._in.valueAt(index++)) { }
            }
            else {
                for (; IonText_1.is_whitespace(ch) || ch === CH_FORWARD_SLASH; ch = this._in.valueAt(index++)) {
                    if (ch === CH_FORWARD_SLASH) {
                        ch = this._in.valueAt(index++);
                        switch (ch) {
                            case CH_FORWARD_SLASH:
                                index = this.indexToNewLine(index);
                                break;
                            case CH_AS:
                                index = this.indexToCloseComment(index);
                                break;
                            default:
                                index--;
                                break;
                        }
                    }
                }
            }
            return index;
        }
        indexToNewLine(index) {
            let ch = this._in.valueAt(index);
            while (ch !== EOF && ch !== CH_NL) {
                if (ch === CH_CR) {
                    if (this._in.valueAt(index + 1) !== CH_NL) {
                        return index;
                    }
                }
                ch = this._in.valueAt(index++);
            }
            return index;
        }
        indexToCloseComment(index) {
            while (this._in.valueAt(index) !== CH_AS &&
                this._in.valueAt(index + 1) !== CH_FORWARD_SLASH) {
                index++;
            }
            return index;
        }
        _skip_triple_quote_gap(entryIndex, end, acceptComments) {
            let tempIndex = entryIndex + 3;
            tempIndex = this.indexWhiteSpace(tempIndex, acceptComments);
            if (tempIndex + 2 <= end && this.verifyTriple(tempIndex)) {
                return tempIndex + 4;
            }
            else {
                return tempIndex + 1;
            }
        }
        readClobEscapes(ii, end) {
            let ch;
            if (ii + 1 >= end) {
                throw new Error("invalid escape sequence");
            }
            ch = this._in.valueAt(ii + 1);
            this._esc_len = 1;
            switch (ch) {
                case ESC_0:
                    return 0;
                case ESC_a:
                    return 7;
                case ESC_b:
                    return 8;
                case ESC_t:
                    return 9;
                case ESC_nl:
                    return 10;
                case ESC_ff:
                    return 12;
                case ESC_cr:
                    return 13;
                case ESC_v:
                    return 11;
                case ESC_dq:
                    return 34;
                case ESC_sq:
                    return 39;
                case ESC_qm:
                    return 63;
                case ESC_bs:
                    return 92;
                case ESC_fs:
                    return 47;
                case ESC_nl2:
                    return -1;
                case ESC_nl3:
                    if (ii + 2 < end && this._in.valueAt(ii + 2) == CH_NL) {
                        this._esc_len = 2;
                    }
                    return IonText_1.ESCAPED_NEWLINE;
                case ESC_x:
                    if (ii + 3 >= end) {
                        throw new Error("invalid escape sequence");
                    }
                    ch = this._get_N_hexdigits(ii + 2, ii + 4);
                    this._esc_len = 3;
                    break;
                default:
                    throw new Error("Invalid escape: /" + ch);
            }
            return ch;
        }
        _read_escape_sequence(ii, end) {
            let ch;
            if (ii + 1 >= end) {
                throw new Error("Invalid escape sequence.");
            }
            ch = this._in.valueAt(ii + 1);
            this._esc_len = 1;
            switch (ch) {
                case ESC_0:
                    return 0;
                case ESC_a:
                    return 7;
                case ESC_b:
                    return 8;
                case ESC_t:
                    return 9;
                case ESC_nl:
                    return 10;
                case ESC_ff:
                    return 12;
                case ESC_cr:
                    return 13;
                case ESC_v:
                    return 11;
                case ESC_dq:
                    return 34;
                case ESC_sq:
                    return 39;
                case ESC_qm:
                    return 63;
                case ESC_bs:
                    return 92;
                case ESC_fs:
                    return 47;
                case ESC_nl2:
                    return -1;
                case ESC_nl3:
                    if (ii + 2 < end && this._in.valueAt(ii + 2) == CH_NL) {
                        this._esc_len = 2;
                    }
                    return IonText_1.ESCAPED_NEWLINE;
                case ESC_x:
                    if (ii + 3 >= end) {
                        throw new Error("invalid escape sequence");
                    }
                    ch = this._get_N_hexdigits(ii + 2, ii + 4);
                    this._esc_len = 3;
                    break;
                case ESC_u:
                    if (ii + 5 >= end) {
                        throw new Error("invalid escape sequence");
                    }
                    ch = this._get_N_hexdigits(ii + 2, ii + 6);
                    this._esc_len = 5;
                    break;
                case ESC_U:
                    if (ii + 9 >= end) {
                        throw new Error("invalid escape sequence");
                    }
                    ch = this._get_N_hexdigits(ii + 2, ii + 10);
                    this._esc_len = 9;
                    break;
                default:
                    throw new Error("unexpected character after escape slash");
            }
            return ch;
        }
        _get_N_hexdigits(ii, end) {
            let ch, v = 0;
            while (ii < end) {
                ch = this._in.valueAt(ii);
                v = v * 16 + get_hex_value(ch);
                ii++;
            }
            return v;
        }
        _value_push(t) {
            if (this._value_type !== ERROR) {
                this._error("unexpected double push of value type!");
            }
            this._value_type = t;
        }
        _value_pop() {
            const t = this._value_type;
            this._value_type = ERROR;
            return t;
        }
        _run() {
            let op;
            while (this._ops.length > 0 && this._value_type === ERROR) {
                op = this._ops.shift();
                op.call(this);
            }
        }
        _read() {
            const ch = this._in.next();
            return ch;
        }
        _read_skipping_comments() {
            let ch = this._read();
            if (ch == CH_FORWARD_SLASH) {
                ch = this._read();
                if (ch == CH_FORWARD_SLASH) {
                    this._read_to_newline();
                    ch = IonText_1.WHITESPACE_COMMENT1;
                }
                else if (ch == CH_AS) {
                    this._read_to_close_comment();
                    ch = IonText_1.WHITESPACE_COMMENT2;
                }
                else {
                    this._unread(ch);
                    ch = CH_FORWARD_SLASH;
                }
            }
            return ch;
        }
        _read_to_newline() {
            let ch;
            for (;;) {
                ch = this._read();
                if (ch == EOF) {
                    break;
                }
                if (ch == CH_NL) {
                    break;
                }
                if (ch == CH_CR) {
                    ch = this._read();
                    if (ch != CH_NL) {
                        this._unread(ch);
                    }
                    break;
                }
            }
        }
        _read_to_close_comment() {
            let ch;
            for (;;) {
                ch = this._read();
                if (ch == EOF) {
                    break;
                }
                if (ch == CH_AS) {
                    ch = this._read();
                    if (ch == CH_FORWARD_SLASH) {
                        break;
                    }
                }
            }
        }
        _unread(ch) {
            this._in.unread(ch);
        }
        _read_after_whitespace(recognize_comments) {
            let ch;
            if (recognize_comments) {
                ch = this._read_skipping_comments();
                while (IonText_1.is_whitespace(ch)) {
                    ch = this._read_skipping_comments();
                }
            }
            else {
                ch = this._read();
                while (IonText_1.is_whitespace(ch)) {
                    ch = this._read();
                }
            }
            return ch;
        }
        _peek(expected) {
            let ch, ii = 0;
            if (expected === undefined || expected.length < 1) {
                return this._in.valueAt(this._in.position());
            }
            while (ii < expected.length) {
                ch = this._read();
                if (ch != expected.charCodeAt(ii)) {
                    break;
                }
                ii++;
            }
            if (ii === expected.length) {
                ch = this._peek();
            }
            else {
                this._unread(ch);
                ch = ERROR;
            }
            while (ii > 0) {
                ii--;
                this._unread(expected.charCodeAt(ii));
            }
            return ch;
        }
        _peek_4_digits(ch1) {
            let ii, ch, is_digits = true;
            const chars = [];
            if (!IonText_1.is_digit(ch1)) {
                return ERROR;
            }
            for (ii = 0; ii < 3; ii++) {
                ch = this._read();
                chars.push(ch);
                if (!IonText_1.is_digit(ch)) {
                    is_digits = false;
                    break;
                }
            }
            ch = is_digits && ii == 3 ? this._peek() : ERROR;
            while (chars.length > 0) {
                this._unread(chars.pop());
            }
            return ch;
        }
        _read_required_digits(ch) {
            if (!IonText_1.is_digit(ch)) {
                return ERROR;
            }
            for (;;) {
                ch = this._read();
                if (!IonText_1.is_digit(ch)) {
                    break;
                }
            }
            return ch;
        }
        _read_optional_digits(ch) {
            while (IonText_1.is_digit(ch)) {
                ch = this._read();
            }
            return ch;
        }
        _readNDigits(n) {
            let ch;
            if (n <= 0) {
                throw new Error("Cannot read a lack of or negative number of digits.");
            }
            while (n--) {
                if (!IonText_1.is_digit((ch = this._read()))) {
                    throw new Error("Expected digit, got: " + String.fromCharCode(ch));
                }
            }
            return ch;
        }
        _readPastNDigits(n) {
            this._readNDigits(n);
            return this._read();
        }
        _read_required_hex_digits(ch) {
            if (!IonText_1.is_hex_digit(ch)) {
                return ERROR;
            }
            for (;;) {
                ch = this._read();
                if (!IonText_1.is_hex_digit(ch)) {
                    break;
                }
            }
            return ch;
        }
        _read_N_hexdigits(n) {
            let ch, ii = 0;
            while (ii < n) {
                ch = this._read();
                if (!IonText_1.is_hex_digit(ch)) {
                    this._error("" + n + " digits required " + ii + " found");
                    return ERROR;
                }
                ii++;
            }
            return ch;
        }
        _parseSymbolId(s) {
            if (s[0] !== "$") {
                return NaN;
            }
            for (let i = 1; i < s.length; i++) {
                if (s[i] < "0" || s[i] > "9") {
                    return NaN;
                }
            }
            return parseInt(s.substr(1, s.length));
        }
        _error(msg) {
            this._ops.unshift(this._done_with_error);
            this._error_msg = msg;
        }
    }
    exports.ParserTextRaw = ParserTextRaw;
});
//# sourceMappingURL=IonParserTextRaw.js.map