/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
define(["require", "exports", "./util"], function (require, exports, util_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.Decimal = void 0;
    class Decimal {
        constructor(coefficient, exponent, isNegative = false) {
            if (typeof coefficient === "string") {
                return Decimal.parse(coefficient);
            }
            if (!util_1._hasValue(exponent)) {
                throw new Error("Decimal's constructor was called with a numeric coefficient but no exponent.");
            }
            if (typeof coefficient === "number") {
                return Decimal._fromNumberCoefficient(coefficient, exponent);
            }
            if (typeof coefficient === "bigint") {
                if (!util_1._hasValue(isNegative)) {
                    isNegative = coefficient < 0n;
                }
                else if (isNegative != coefficient < 0n) {
                    coefficient *= -1n;
                }
                return Decimal._fromBigIntCoefficient(isNegative, coefficient, exponent);
            }
            throw new Error(`Unsupported parameter set (${coefficient}, ${exponent}, ${isNegative} passed to Decimal constructor.`);
        }
        static _fromNumberCoefficient(coefficient, exponent) {
            if (!Number.isInteger(coefficient)) {
                throw new Error("The provided coefficient was not an integer. (" + coefficient + ")");
            }
            const isNegative = coefficient < 0 || Object.is(coefficient, -0);
            return this._fromBigIntCoefficient(isNegative, BigInt(coefficient), exponent);
        }
        static _fromBigIntCoefficient(isNegative, coefficient, exponent) {
            const value = Object.create(this.prototype);
            value._initialize(isNegative, coefficient, exponent);
            return value;
        }
        static parse(str) {
            let exponent = 0;
            if (str === "null" || str === "null.decimal") {
                return null;
            }
            const d = str.match("[d|D]");
            let exponentDelimiterIndex = str.length;
            if (d !== undefined && d !== null) {
                exponent = Number(str.substring(d.index + 1, str.length));
                exponentDelimiterIndex = d.index;
            }
            const f = str.match("\\.");
            let coefficientText;
            if (f) {
                const exponentShift = d
                    ? d.index - 1 - f.index
                    : str.length - 1 - f.index;
                exponent -= exponentShift;
                coefficientText =
                    str.substring(0, f.index) +
                        str.substring(f.index + 1, exponentDelimiterIndex);
            }
            else {
                coefficientText = str.substring(0, exponentDelimiterIndex);
            }
            const coefficient = BigInt(coefficientText);
            const isNegative = coefficient < 0n || coefficientText.startsWith("-0");
            return Decimal._fromBigIntCoefficient(isNegative, coefficient, exponent);
        }
        isNegative() {
            return this._isNegative;
        }
        numberValue() {
            if (this._isNegativeZero()) {
                return -0;
            }
            return Number(this._coefficient) * Math.pow(10, this._exponent);
        }
        intValue() {
            return Math.trunc(this.numberValue());
        }
        toString() {
            let cStr = this._coefficient.toString();
            if (cStr[0] === "-") {
                cStr = cStr.substring(1, cStr.length);
            }
            const precision = cStr.length;
            const adjustedExponent = this._exponent + (precision - 1);
            let s = "";
            if (this._exponent <= 0 && adjustedExponent >= -6) {
                if (this._exponent === 0) {
                    s += cStr;
                }
                else {
                    if (cStr.length <= -this._exponent) {
                        cStr = "0".repeat(-this._exponent - cStr.length + 1) + cStr;
                        s += cStr.substring(0, 1) + "." + cStr.substring(1);
                    }
                    else {
                        s +=
                            cStr.substring(0, precision + this._exponent) +
                                "." +
                                cStr.substring(precision + this._exponent);
                    }
                }
            }
            else {
                s += cStr[0];
                if (cStr.length > 1) {
                    s += "." + cStr.substring(1);
                }
                s += "E" + (adjustedExponent > 0 ? "+" : "") + adjustedExponent;
            }
            return (this.isNegative() ? "-" : "") + s;
        }
        toJSON() {
            return this.numberValue();
        }
        getCoefficient() {
            return this._coefficient;
        }
        getExponent() {
            return this._exponent;
        }
        equals(that) {
            return (this.getExponent() === that.getExponent() &&
                util_1._sign(this.getExponent()) === util_1._sign(that.getExponent()) &&
                this.isNegative() === that.isNegative() &&
                this.getCoefficient() === that.getCoefficient());
        }
        compareTo(that) {
            if (this._coefficient === 0n && that._coefficient === 0n) {
                return 0;
            }
            const neg = this.isNegative();
            if (neg !== that.isNegative()) {
                return neg ? -1 : 1;
            }
            let [thisCoefficientStr, thisPrecision, thisMagnitude,] = this._compareToParams();
            let [thatCoefficientStr, thatPrecision, thatMagnitude,] = that._compareToParams();
            if (thisMagnitude > thatMagnitude) {
                return neg ? -1 : 1;
            }
            else if (thisMagnitude < thatMagnitude) {
                return neg ? 1 : -1;
            }
            if (thisCoefficientStr.length < thatCoefficientStr.length) {
                thisCoefficientStr += "0".repeat(thatPrecision - thisPrecision);
            }
            else if (thisCoefficientStr.length > thatCoefficientStr.length) {
                thatCoefficientStr += "0".repeat(thisPrecision - thatPrecision);
            }
            const thisBigInt = BigInt(thisCoefficientStr);
            const thatBigInt = BigInt(thatCoefficientStr);
            if (thisBigInt > thatBigInt) {
                return neg ? -1 : 1;
            }
            else if (thisBigInt < thatBigInt) {
                return neg ? 1 : -1;
            }
            return 0;
        }
        _initialize(isNegative, coefficient, exponent) {
            this._isNegative = isNegative;
            this._coefficient = coefficient;
            if (Object.is(-0, exponent)) {
                exponent = 0;
            }
            this._exponent = exponent;
        }
        _isNegativeZero() {
            return this.isNegative() && this._coefficient == 0n;
        }
        _compareToParams() {
            const coefficientStr = this.isNegative()
                ? this._coefficient.toString().substring(1)
                : this._coefficient.toString();
            const precision = coefficientStr.length;
            let magnitude = precision + this._exponent;
            if (magnitude <= 0) {
                magnitude -= 1;
            }
            if (this._coefficient === 0n) {
                magnitude = -Infinity;
            }
            return [coefficientStr, precision, magnitude];
        }
    }
    exports.Decimal = Decimal;
    Decimal.ZERO = new Decimal(0, 0);
    Decimal.ONE = new Decimal(1, 0);
});
//# sourceMappingURL=IonDecimal.js.map