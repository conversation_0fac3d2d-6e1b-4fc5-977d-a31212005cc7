/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
export declare const WHITESPACE_COMMENT1 = -2;
export declare const WHITESPACE_COMMENT2 = -3;
export declare const ESCAPED_NEWLINE = -4;
export declare function is_digit(ch: number): boolean;
export declare function is_keyword(str: string): boolean;
export declare function asAscii(s: any): string;
export declare function nextEscape(s: string, prev: number): number;
export declare function needsEscape(c: number): boolean;
export declare function escapeString(s: string, pos: number): string;
export declare function escapeSequence(c: number): string;
export declare function toHex(c: number, len: number): string;
export declare function is_letter(ch: number): boolean;
export declare function isNumericTerminator(ch: number): boolean;
export declare function is_letter_or_digit(ch: number): boolean;
export declare function is_operator_char(ch: number): boolean;
export declare function is_whitespace(ch: number): boolean;
export declare function is_base64_char(ch: number): boolean;
export declare function is_hex_digit(ch: number): boolean;
export declare function fromBase64(str: string): Uint8Array;
export declare function toBase64(buf: Uint8Array): string;
export declare enum CharCodes {
    NULL = 0,
    BELL = 7,
    BACKSPACE = 8,
    HORIZONTAL_TAB = 9,
    LINE_FEED = 10,
    VERTICAL_TAB = 11,
    FORM_FEED = 12,
    CARRIAGE_RETURN = 13,
    DOUBLE_QUOTE = 34,
    SINGLE_QUOTE = 39,
    FORWARD_SLASH = 47,
    QUESTION_MARK = 63,
    BACKSLASH = 92,
    LEFT_PARENTHESIS = 40,
    RIGHT_PARENTHESIS = 41,
    LEFT_BRACE = 123,
    RIGHT_BRACE = 125,
    LEFT_BRACKET = 91,
    RIGHT_BRACKET = 93,
    COMMA = 44,
    SPACE = 32,
    LOWERCASE_X = 120,
    COLON = 58
}
export interface EscapeIndex {
    [index: number]: number[];
}
export declare let ClobEscapes: EscapeIndex;
export declare let StringEscapes: EscapeIndex;
export declare let SymbolEscapes: EscapeIndex;
export declare function isIdentifier(s: string): boolean;
export declare function isOperator(s: string): boolean;
export declare function isDigit(charCode: number): boolean;
export declare function escape(input: string, escapes: EscapeIndex): string;
