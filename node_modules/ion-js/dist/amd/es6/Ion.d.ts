/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
import IntSize from "./IntSize";
import { Catalog } from "./IonCatalog";
import { Reader } from "./IonReader";
import { Writer } from "./IonWriter";
export declare type ReaderOctetBuffer = ArrayBufferLike | ArrayLike<number>;
export declare type ReaderBuffer = ReaderOctetBuffer | string;
export declare function makeReader(buf: ReaderBuffer, catalog?: Catalog): Reader;
export declare function makeTextWriter(): Writer;
export declare function makePrettyWriter(indentSize?: number): Writer;
export declare function makeBinaryWriter(): Writer;
export declare function dumpBinary(...values: any[]): Uint8Array;
export declare function dumpText(...values: any[]): string;
export declare function dumpPrettyText(...values: any[]): string;
export { Reader, ReaderScalarValue } from "./IonReader";
export { Writer } from "./IonWriter";
export { Catalog } from "./IonCatalog";
export { Decimal } from "./IonDecimal";
export { defaultLocalSymbolTable } from "./IonLocalSymbolTable";
export { IntSize };
export { IonType } from "./IonType";
export { IonTypes } from "./IonTypes";
export { SharedSymbolTable } from "./IonSharedSymbolTable";
export { TimestampPrecision, Timestamp } from "./IonTimestamp";
export { toBase64 } from "./IonText";
export { decodeUtf8 } from "./IonUnicode";
import * as dom from "./dom";
export { dom };
export { load, loadAll } from "./dom";
export { IonEvent, IonEventType, IonEventFactory } from "./events/IonEvent";
export { IonEventStream } from "./events/IonEventStream";
export { EventStreamError } from "./events/EventStreamError";
export { ComparisonResult, ComparisonResultType } from "./ComparisonResult";
