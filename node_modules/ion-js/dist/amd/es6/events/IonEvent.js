/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
define(["require", "exports", "../ComparisonResult", "../IonBinaryWriter", "../IonLocalSymbolTable", "../IonTextWriter", "../IonTypes", "../IonUnicode", "../IonWriteable"], function (require, exports, ComparisonResult_1, IonBinaryWriter_1, IonLocalSymbolTable_1, IonTextWriter_1, IonTypes_1, IonUnicode_1, IonWriteable_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.IonEventFactory = exports.IonEventType = void 0;
    var IonEventType;
    (function (IonEventType) {
        IonEventType[IonEventType["SCALAR"] = 0] = "SCALAR";
        IonEventType[IonEventType["CONTAINER_START"] = 1] = "CONTAINER_START";
        IonEventType[IonEventType["CONTAINER_END"] = 2] = "CONTAINER_END";
        IonEventType[IonEventType["SYMBOL_TABLE"] = 3] = "SYMBOL_TABLE";
        IonEventType[IonEventType["STREAM_END"] = 4] = "STREAM_END";
    })(IonEventType = exports.IonEventType || (exports.IonEventType = {}));
    class AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            this.eventType = eventType;
            this.ionType = ionType;
            this.fieldName = fieldName;
            this.annotations = annotations;
            this.depth = depth;
            this.ionValue = ionValue;
        }
        write(writer) {
            writer.stepIn(IonTypes_1.IonTypes.STRUCT);
            writer.writeFieldName("event_type");
            writer.writeSymbol(IonEventType[this.eventType]);
            if (this.ionType !== null) {
                writer.writeFieldName("ion_type");
                writer.writeSymbol(this.ionType.name.toUpperCase());
            }
            if (this.fieldName !== null && this.fieldName !== undefined) {
                writer.writeFieldName("field_name");
                writer.stepIn(IonTypes_1.IonTypes.STRUCT);
                writer.writeFieldName("text");
                writer.writeString(this.fieldName);
                writer.stepOut();
            }
            if (this.annotations !== null) {
                writer.writeFieldName("annotations");
                this.writeAnnotations(writer);
            }
            if (this.eventType === IonEventType.SCALAR) {
                this.writeValues(writer);
            }
            writer.writeFieldName("depth");
            writer.writeInt(this.depth);
            writer.stepOut();
        }
        writeAnnotations(writer) {
            if (this.annotations === undefined) {
                writer.writeNull(IonTypes_1.IonTypes.LIST);
                return;
            }
            writer.stepIn(IonTypes_1.IonTypes.LIST);
            for (let i = 0; i < this.annotations.length; i++) {
                writer.stepIn(IonTypes_1.IonTypes.STRUCT);
                writer.writeFieldName("text");
                writer.writeString(this.annotations[i]);
                writer.stepOut();
            }
            writer.stepOut();
        }
        writeSymbolToken(writer, text) {
            writer.writeSymbol(text);
        }
        writeImportDescriptor(writer) {
            writer.writeNull(IonTypes_1.IonTypes.STRUCT);
        }
        writeValues(writer) {
            if (this.eventType === IonEventType.SCALAR) {
                writer.writeFieldName("value_text");
                this.writeTextValue(writer);
                writer.writeFieldName("value_binary");
                this.writeBinaryValue(writer);
            }
        }
        writeTextValue(writer) {
            const tempTextWriter = new IonTextWriter_1.TextWriter(new IonWriteable_1.Writeable());
            this.writeIonValue(tempTextWriter);
            tempTextWriter.close();
            writer.writeString(IonUnicode_1.decodeUtf8(tempTextWriter.getBytes()));
        }
        writeBinaryValue(writer) {
            const tempBinaryWriter = new IonBinaryWriter_1.BinaryWriter(IonLocalSymbolTable_1.defaultLocalSymbolTable(), new IonWriteable_1.Writeable());
            this.writeIonValue(tempBinaryWriter);
            tempBinaryWriter.close();
            const binaryBuffer = tempBinaryWriter.getBytes();
            writer.stepIn(IonTypes_1.IonTypes.LIST);
            for (let i = 0; i < binaryBuffer.length; i++) {
                writer.writeInt(binaryBuffer[i]);
            }
            writer.stepOut();
        }
        equals(expected) {
            return this.compare(expected).result == ComparisonResult_1.ComparisonResultType.EQUAL;
        }
        compare(expected) {
            if (this.eventType !== expected.eventType) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Event types don't match");
            }
            if (this.ionType !== expected.ionType) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Ion types don't match " +
                    this.ionType?.name +
                    " vs. " +
                    expected.ionType?.name);
            }
            if (this.fieldName !== expected.fieldName) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Field names don't match " +
                    this.fieldName +
                    " vs. " +
                    expected.fieldName);
            }
            if (this.depth !== expected.depth) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Event depths don't match " + this.depth + " vs. " + expected.depth);
            }
            const annotationResult = this.annotationCompare(expected.annotations);
            if (annotationResult.result === ComparisonResult_1.ComparisonResultType.NOT_EQUAL) {
                return annotationResult;
            }
            const valueResult = this.valueCompare(expected);
            if (valueResult.result === ComparisonResult_1.ComparisonResultType.NOT_EQUAL) {
                return valueResult;
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
        }
        annotationCompare(expectedAnnotations) {
            if (this.annotations === expectedAnnotations) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            if (this.annotations.length !== expectedAnnotations.length) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "annotations length don't match" +
                    this.annotations.length +
                    " vs. " +
                    expectedAnnotations.length);
            }
            for (let i = 0; i < this.annotations.length; i++) {
                if (this.annotations[i] !== expectedAnnotations[i]) {
                    return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "annotation value doesn't match" +
                        this.annotations[i] +
                        " vs. " +
                        expectedAnnotations[i]);
                }
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
        }
    }
    class IonEventFactory {
        makeEvent(eventType, ionType, fieldName, depth, annotations, isNull, value) {
            if (isNull) {
                return new IonNullEvent(eventType, ionType, fieldName, annotations, depth);
            }
            switch (eventType) {
                case IonEventType.SCALAR:
                case IonEventType.CONTAINER_START:
                    switch (ionType) {
                        case IonTypes_1.IonTypes.BOOL: {
                            return new IonBoolEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.INT: {
                            return new IonIntEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.FLOAT: {
                            return new IonFloatEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.DECIMAL: {
                            return new IonDecimalEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.SYMBOL: {
                            return new IonSymbolEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.STRING: {
                            return new IonStringEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.TIMESTAMP: {
                            return new IonTimestampEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.BLOB: {
                            return new IonBlobEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.CLOB: {
                            return new IonClobEvent(eventType, ionType, fieldName, annotations, depth, value);
                        }
                        case IonTypes_1.IonTypes.LIST: {
                            return new IonListEvent(eventType, ionType, fieldName, annotations, depth);
                        }
                        case IonTypes_1.IonTypes.SEXP: {
                            return new IonSexpEvent(eventType, ionType, fieldName, annotations, depth);
                        }
                        case IonTypes_1.IonTypes.STRUCT: {
                            return new IonStructEvent(eventType, ionType, fieldName, annotations, depth);
                        }
                        default: {
                            throw new Error("IonType " + ionType.name + " unexpected.");
                        }
                    }
                case IonEventType.SYMBOL_TABLE:
                    throw new Error("symbol tables unsupported.");
                case IonEventType.CONTAINER_END:
                    return new IonEndEvent(eventType, depth, ionType);
                case IonEventType.STREAM_END:
                    return new IonEndEvent(eventType, depth, ionType);
            }
        }
    }
    exports.IonEventFactory = IonEventFactory;
    class IonNullEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth) {
            super(eventType, ionType, fieldName, annotations, depth, null);
        }
        valueCompare(expected) {
            if (expected instanceof IonNullEvent &&
                this.ionValue === expected.ionValue) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
        }
        writeIonValue(writer) {
            writer.writeNull(this.ionType !== null ? this.ionType : IonTypes_1.IonTypes.NULL);
        }
    }
    class IonIntEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (expected instanceof IonIntEvent) {
                let actualValue = typeof this.ionValue === "bigint"
                    ? this.ionValue
                    : BigInt(this.ionValue);
                let expectedValue = typeof expected.ionValue === "bigint"
                    ? expected.ionValue
                    : BigInt(expected.ionValue);
                if (actualValue === expectedValue) {
                    return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
                }
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, `${this.ionValue} vs. ${expected.ionValue}`);
        }
        writeIonValue(writer) {
            writer.writeInt(this.ionValue);
        }
    }
    class IonBoolEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (expected instanceof IonBoolEvent &&
                this.ionValue === expected.ionValue) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
        }
        writeIonValue(writer) {
            writer.writeBoolean(this.ionValue);
        }
    }
    class IonFloatEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (expected instanceof IonFloatEvent &&
                Object.is(this.ionValue, expected.ionValue)) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
        }
        writeIonValue(writer) {
            writer.writeFloat64(this.ionValue);
        }
    }
    class IonDecimalEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (expected instanceof IonDecimalEvent &&
                this.ionValue.equals(expected.ionValue)) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
        }
        writeIonValue(writer) {
            writer.writeDecimal(this.ionValue);
        }
    }
    class IonSymbolEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (expected instanceof IonSymbolEvent &&
                this.ionValue === expected.ionValue) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
        }
        writeIonValue(writer) {
            writer.writeSymbol(this.ionValue);
        }
    }
    class IonStringEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (expected instanceof IonStringEvent &&
                this.ionValue === expected.ionValue) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
        }
        writeIonValue(writer) {
            writer.writeString(this.ionValue);
        }
    }
    class IonTimestampEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (expected instanceof IonTimestampEvent &&
                this.ionValue.equals(expected.ionValue)) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
        }
        writeIonValue(writer) {
            writer.writeTimestamp(this.ionValue);
        }
    }
    class IonBlobEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (!(expected instanceof IonBlobEvent)) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL);
            }
            if (this.ionValue.length !== expected.ionValue.length) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Blob length don't match");
            }
            for (let i = 0; i < this.ionValue.length; i++) {
                if (this.ionValue[i] !== expected.ionValue[i]) {
                    return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue[i] + " vs. " + expected.ionValue[i]);
                }
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
        }
        writeIonValue(writer) {
            writer.writeBlob(this.ionValue);
        }
    }
    class IonClobEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth, ionValue) {
            super(eventType, ionType, fieldName, annotations, depth, ionValue);
        }
        valueCompare(expected) {
            if (!(expected instanceof IonClobEvent)) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL);
            }
            if (this.ionValue.length !== expected.ionValue.length) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
            }
            for (let i = 0; i < this.ionValue.length; i++) {
                if (this.ionValue[i] !== expected.ionValue[i]) {
                    return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue[i] + " vs. " + expected.ionValue[i]);
                }
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
        }
        writeIonValue(writer) {
            writer.writeClob(this.ionValue);
        }
    }
    class AbsIonContainerEvent extends AbstractIonEvent {
        constructor(eventType, ionType, fieldName, annotations, depth) {
            super(eventType, ionType, fieldName, annotations, depth, null);
        }
        writeIonValue(writer) {
        }
    }
    class IonStructEvent extends AbsIonContainerEvent {
        constructor(eventType, ionType, fieldName, annotations, depth) {
            super(eventType, ionType, fieldName, annotations, depth);
        }
        valueCompare(expected) {
            if (!(expected instanceof IonStructEvent)) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Event types don't match");
            }
            const container = this.ionValue == null ? [] : this.ionValue;
            const expectedContainer = expected.ionValue == null ? [] : expected.ionValue;
            if (container.length !== expectedContainer.length) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Struct length don't match");
            }
            return this.structsCompare(container, expectedContainer);
        }
        structsCompare(actualEvents, expectedEvents) {
            let matchFound = new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            const paired = new Array(expectedEvents.length);
            for (let i = 0; matchFound && i < actualEvents.length; i++) {
                matchFound.result = ComparisonResult_1.ComparisonResultType.NOT_EQUAL;
                for (let j = 0; matchFound.result == ComparisonResult_1.ComparisonResultType.NOT_EQUAL &&
                    j < expectedEvents.length; j++) {
                    if (!paired[j]) {
                        const child = actualEvents[i];
                        const expectedChild = expectedEvents[j];
                        matchFound = child.compare(expectedChild);
                        if (matchFound.result == ComparisonResult_1.ComparisonResultType.EQUAL) {
                            paired[j] = true;
                        }
                        if (matchFound.result == ComparisonResult_1.ComparisonResultType.EQUAL &&
                            child.eventType === IonEventType.CONTAINER_START) {
                            for (let k = 0; k < expectedChild.ionValue.length; k++) {
                                paired[k + j + 1] = true;
                            }
                            i += child.ionValue.length;
                        }
                    }
                }
            }
            for (let i = 0; i < paired.length; i++) {
                if (!paired[i]) {
                    matchFound = new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Didn't find matching field for " + expectedEvents[i].fieldName);
                    break;
                }
            }
            return matchFound;
        }
    }
    class IonListEvent extends AbsIonContainerEvent {
        constructor(eventType, ionType, fieldName, annotations, depth) {
            super(eventType, ionType, fieldName, annotations, depth);
        }
        valueCompare(expected) {
            if (!(expected instanceof IonListEvent)) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Event types don't match");
            }
            const container = this.ionValue == null ? [] : this.ionValue;
            const expectedContainer = expected.ionValue == null ? [] : expected.ionValue;
            if (container.length !== expectedContainer.length) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "List length don't match");
            }
            for (let i = 0; i < container.length; i++) {
                const child = container[i];
                if (child.compare(expectedContainer[i]).result ==
                    ComparisonResult_1.ComparisonResultType.NOT_EQUAL) {
                    return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, `${child.ionValue} vs. ${expectedContainer[i].ionValue}`, i + 1, i + 1);
                }
                else if (child.eventType === IonEventType.CONTAINER_START) {
                    i += child.ionValue.length;
                }
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
        }
    }
    class IonSexpEvent extends AbsIonContainerEvent {
        constructor(eventType, ionType, fieldName, annotations, depth) {
            super(eventType, ionType, fieldName, annotations, depth);
        }
        valueCompare(expected) {
            if (!(expected instanceof IonSexpEvent)) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "Event types don't match");
            }
            const container = this.ionValue == null ? [] : this.ionValue;
            const expectedContainer = expected.ionValue == null ? [] : expected.ionValue;
            if (container.length !== expectedContainer.length) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, "S-expression length don't match");
            }
            for (let i = 0; i < container.length; i++) {
                const child = container[i];
                const eventResult = child.compare(expectedContainer[i]);
                if (eventResult.result == ComparisonResult_1.ComparisonResultType.NOT_EQUAL) {
                    return eventResult;
                }
                else if (child.eventType === IonEventType.CONTAINER_START) {
                    i += child.ionValue.length;
                }
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
        }
    }
    class IonEndEvent extends AbstractIonEvent {
        constructor(eventType, depth, ionType) {
            if (eventType === IonEventType.STREAM_END) {
                super(eventType, null, null, [], depth, undefined);
            }
            else {
                super(eventType, ionType, null, [], depth, undefined);
            }
        }
        valueCompare(expected) {
            if (expected instanceof IonEndEvent &&
                this.ionValue === expected.ionValue) {
                return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.EQUAL);
            }
            return new ComparisonResult_1.ComparisonResult(ComparisonResult_1.ComparisonResultType.NOT_EQUAL, this.ionValue + " vs. " + expected.ionValue);
        }
        writeIonValue(writer) {
        }
    }
});
//# sourceMappingURL=IonEvent.js.map