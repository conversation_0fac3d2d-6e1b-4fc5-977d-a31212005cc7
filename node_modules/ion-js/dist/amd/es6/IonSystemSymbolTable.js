/*!
 * Copyright 2012 Amazon.com, Inc. or its affiliates. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License").
 * You may not use this file except in compliance with the License.
 * A copy of the License is located at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * or in the "license" file accompanying this file. This file is distributed
 * on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
 * express or implied. See the License for the specific language governing
 * permissions and limitations under the License.
 */
define(["require", "exports", "./IonImport", "./IonSharedSymbolTable"], function (require, exports, IonImport_1, IonSharedSymbolTable_1) {
    "use strict";
    Object.defineProperty(exports, "__esModule", { value: true });
    exports.getSystemSymbolTableImport = exports.getSystemSymbolTable = void 0;
    const systemSymbolTable = new IonSharedSymbolTable_1.SharedSymbolTable("$ion", 1, [
        "$ion",
        "$ion_1_0",
        "$ion_symbol_table",
        "name",
        "version",
        "imports",
        "symbols",
        "max_id",
        "$ion_shared_symbol_table",
    ]);
    function getSystemSymbolTable() {
        return systemSymbolTable;
    }
    exports.getSystemSymbolTable = getSystemSymbolTable;
    function getSystemSymbolTableImport() {
        return new IonImport_1.Import(null, getSystemSymbolTable());
    }
    exports.getSystemSymbolTableImport = getSystemSymbolTableImport;
});
//# sourceMappingURL=IonSystemSymbolTable.js.map