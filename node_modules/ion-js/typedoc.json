{"name": "Ion-JS Library", "excludePrivate": true, "excludeProtected": true, "exclude": ["src/AbstractWriter.ts", "src/IonConstants.ts", "src/IonImport.ts", "src/IonSubstituteSymbolTable.ts", "src/IonSpan.ts", "src/IonSymbol.ts", "src/IonSymbolIndex.ts", "src/IonUnicode.ts", "src/IonValue.ts", "src/IonWriteable.ts", "src/util.ts", "src/IonCatalog.ts", "src/IonSymbols.ts", "src/Ion*SymbolTable.ts", "src/IonBinary*.ts", "src/Ion+(Pretty|)Text*.ts", "src/*Raw.ts", "src/IonLowLevel*.ts", "src/IonTest*.ts", "src/IonEvent*.ts", "src/BigIntSerde.ts", "src/SignAndMagnitudeInt.ts"], "out": "docs/api/", "hideGenerator": true}