{"name": "ion-js", "version": "5.2.1", "description": "A JavaScript implementation of the Ion data interchange format", "main": "dist/commonjs/es6/Ion.js", "types": "dist/commonjs/es6/Ion.d.ts", "exports": {"module": "./dist/es6/es6/Ion.js", "default": "./dist/commonjs/es6/Ion.js"}, "scripts": {"commit": "git-cz", "prepare": "grunt release", "lint": "grunt lint && prettier --check 'src/**/*.ts'", "lint:fix": "prettier --write 'src/**/*.ts'", "test": "nyc mocha", "release": "grunt release", "test-driver": "cd test-driver && npm install", "build-test-driver": "cd test-driver && npm run build"}, "repository": {"type": "git", "url": "https://github.com/amazon-ion/ion-js.git"}, "keywords": ["ion", "JSON", "data format"], "author": "The Ion Team <<EMAIL>> (https://amazon-ion.github.io/ion-docs/index.html)", "license": "Apache-2.0", "bugs": {"url": "https://github.com/amazon-ion/ion-js/issues"}, "homepage": "https://github.com/amazon-ion/ion-js#readme", "config": {"commitizen": {"path": "node_modules/cz-conventional-changelog"}}, "nyc": {"extends": "@istanbuljs/nyc-config-typescript", "all": true, "include": ["src/**.ts"], "exclude": ["src/.**"], "check-coverage": false}, "devDependencies": {"@babel/cli": "^7.18.10", "@babel/core": "^7.19.1", "@babel/plugin-transform-object-assign": "^7.18.6", "@babel/plugin-transform-runtime": "^7.19.1", "@babel/polyfill": "^7.8.7", "@babel/preset-env": "^7.10.0", "@babel/runtime": "^7.10.0", "@istanbuljs/nyc-config-typescript": "^0.1.3", "@types/chai": "^4.2.11", "@types/mocha": "^5.2.7", "@types/node": "^12.12.42", "@typescript-eslint/eslint-plugin": "^4.33.0", "@typescript-eslint/parser": "^4.33.0", "babelify": "^10.0.0", "chai": "^4.3.6", "commitizen": "^4.2.5", "cz-conventional-changelog": "^3.2.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.5.0", "grunt": "^1.5.3", "grunt-babel": "^8.0.0", "grunt-browserify": "^5.3.0", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^2.0.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-jshint": "^2.1.0", "grunt-contrib-uglify": "^5.2.2", "grunt-eslint": "^23.0.0", "grunt-shell": "^3.0.1", "grunt-ts": "^6.0.0-beta.22", "grunt-typedoc": "^0.2.4", "mocha": "^6.2.3", "mocha-typescript": "^1.1.17", "nyc": "^14.1.1", "prettier": "2.1.2", "semantic-release": "^17.2.3", "source-map-support": "^0.5.19", "ts-node": "^8.10.1", "typedoc": "^0.20.37", "typescript": "^3.9.10", "yargs": "^17.5.1"}, "browserslist": ["Chrome >= 80.0", "Firefox >= 80.0", "Opera >= 63.0", "Edge >= 80.0", "Safari >= 14.1", "Samsung >= 12.0"]}