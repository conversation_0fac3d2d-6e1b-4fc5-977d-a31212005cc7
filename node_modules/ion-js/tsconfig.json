// Base Typescript Compiler Configuration
// Targets CommonJS Module system and ES6 feature set.
{
  "include": ["src/**/*"],
  "exclude": ["**/*.d.ts"],
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "moduleResolution": "node",
    "esModuleInterop": true,
    "lib": ["ESNext"],
    "declaration": true,
    "inlineSources": true,
    "sourceMap": true,
    // TODO enable this to have stricter checking
    "strict": false,
    "strictNullChecks": true,
    "rootDirs": ["src", "test"],
    "outDir": "dist/commonjs/es6",
    "experimentalDecorators": true
  }
}
