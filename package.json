{"name": "ion-log-playback", "version": "1.0.0", "description": "Ion log file playback and visualization tool", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["ion", "log", "playback", "visualization", "robotics"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "ion-js": "^5.2.1", "multer": "^1.4.5-lts.1", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}}