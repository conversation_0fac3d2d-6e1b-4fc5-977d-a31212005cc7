/**
 * Express Server for Ion Log Playback Web Interface
 * Provides REST API and WebSocket support for real-time data streaming
 */

const express = require('express');
const multer = require('multer');
const WebSocket = require('ws');
const http = require('http');
const path = require('path');
const fs = require('fs');

const IonParser = require('./parser');
const TopicReader = require('./topic-reader');

class IonLogServer {
  constructor(port = 3000) {
    this.port = port;
    this.app = express();
    this.server = http.createServer(this.app);
    this.wss = new WebSocket.Server({ server: this.server });

    this.parser = new IonParser();
    this.topicReader = null;
    this.clients = new Set();

    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
  }

  /**
   * Setup Express middleware
   */
  setupMiddleware() {
    // Serve static files from public directory
    this.app.use(express.static(path.join(__dirname, 'public')));

    // Parse JSON bodies
    this.app.use(express.json());

    // Setup file upload handling
    const upload = multer({
      dest: 'uploads/',
      fileFilter: (req, file, cb) => {
        // Accept .ion files and text files
        if (file.originalname.endsWith('.ion') || file.mimetype.startsWith('text/')) {
          cb(null, true);
        } else {
          cb(new Error('Only .ion files are allowed'));
        }
      },
      limits: {
        fileSize: 100 * 1024 * 1024 // 100MB limit
      }
    });

    this.upload = upload;
  }

  /**
   * Setup API routes
   */
  setupRoutes() {
    // Upload and parse Ion file
    this.app.post('/api/upload', this.upload.single('ionFile'), async (req, res) => {
      try {
        if (!req.file) {
          return res.status(400).json({ error: 'No file uploaded' });
        }

        const parsedData = await this.parser.parseFile(req.file.path);

        // Create new topic reader
        this.topicReader = new TopicReader(parsedData);
        this.setupTopicReaderEvents();

        // Clean up uploaded file
        fs.unlinkSync(req.file.path);

        res.json({
          success: true,
          data: {
            topics: parsedData.topics,
            totalEntries: parsedData.totalEntries,
            timeRange: parsedData.timeRange
          }
        });

        // Notify all clients about new data
        this.broadcast({
          type: 'file_loaded',
          data: parsedData
        });

      } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({ error: error.message });
      }
    });

    // Get current playback state
    this.app.get('/api/state', (req, res) => {
      if (!this.topicReader) {
        return res.status(404).json({ error: 'No file loaded' });
      }

      res.json(this.topicReader.getState());
    });

    // Control playback
    this.app.post('/api/playback/:action', (req, res) => {
      if (!this.topicReader) {
        return res.status(404).json({ error: 'No file loaded' });
      }

      const { action } = req.params;
      const { timestamp, speed } = req.body;

      try {
        switch (action) {
          case 'play':
            this.topicReader.play();
            break;
          case 'pause':
            this.topicReader.pause();
            break;
          case 'stop':
            this.topicReader.stop();
            break;
          case 'seek':
            if (timestamp !== undefined) {
              this.topicReader.seekTo(timestamp);
            }
            break;
          case 'speed':
            if (speed !== undefined) {
              this.topicReader.setPlaybackSpeed(speed);
            }
            break;
          default:
            return res.status(400).json({ error: 'Invalid action' });
        }

        res.json({ success: true, state: this.topicReader.getState() });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Get topic data
    this.app.get('/api/topics/:topicName', (req, res) => {
      if (!this.topicReader) {
        return res.status(404).json({ error: 'No file loaded' });
      }

      const { topicName } = req.params;
      const { startTime, endTime } = req.query;

      try {
        const data = this.topicReader.getTopicData(
          topicName,
          startTime ? parseFloat(startTime) : undefined,
          endTime ? parseFloat(endTime) : undefined
        );

        res.json({ topic: topicName, data });
      } catch (error) {
        res.status(500).json({ error: error.message });
      }
    });

    // Get available topics
    this.app.get('/api/topics', (req, res) => {
      if (!this.topicReader) {
        return res.status(404).json({ error: 'No file loaded' });
      }

      res.json({ topics: this.topicReader.getTopics() });
    });

    // Health check
    this.app.get('/api/health', (req, res) => {
      res.json({
        status: 'ok',
        hasData: !!this.topicReader,
        timestamp: new Date().toISOString()
      });
    });

    // Serve main page
    this.app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });
  }

  /**
   * Setup WebSocket for real-time communication
   */
  setupWebSocket() {
    this.wss.on('connection', (ws) => {
      console.log('Client connected');
      this.clients.add(ws);

      // Send current state if available
      if (this.topicReader) {
        ws.send(JSON.stringify({
          type: 'state_update',
          data: this.topicReader.getState()
        }));
      }

      ws.on('close', () => {
        console.log('Client disconnected');
        this.clients.delete(ws);
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        this.clients.delete(ws);
      });
    });
  }

  /**
   * Setup topic reader event handlers
   */
  setupTopicReaderEvents() {
    if (!this.topicReader) return;

    // Subscribe to all topics for broadcasting
    const topics = this.topicReader.getTopics();
    topics.forEach(topic => {
      this.topicReader.subscribe(topic, (entries) => {
        this.broadcast({
          type: 'topic_data',
          topic: topic,
          data: entries
        });
      });
    });

    // Broadcast state changes
    this.topicReader.on('playbackStateChanged', (state) => {
      this.broadcast({
        type: 'playback_state_changed',
        data: state
      });
    });

    this.topicReader.on('timeChanged', (state) => {
      this.broadcast({
        type: 'time_changed',
        data: state
      });
    });

    this.topicReader.on('speedChanged', (state) => {
      this.broadcast({
        type: 'speed_changed',
        data: state
      });
    });
  }

  /**
   * Broadcast message to all connected clients
   * @param {Object} message - Message to broadcast
   */
  broadcast(message) {
    const messageStr = JSON.stringify(message);
    this.clients.forEach(client => {
      if (client.readyState === WebSocket.OPEN) {
        try {
          client.send(messageStr);
        } catch (error) {
          console.error('Error sending message to client:', error);
          this.clients.delete(client);
        }
      }
    });
  }

  /**
   * Start the server
   */
  start() {
    this.server.listen(this.port, () => {
      console.log(`Ion Log Playback Server running on http://localhost:${this.port}`);
      console.log(`WebSocket server running on ws://localhost:${this.port}`);
    });
  }

  /**
   * Stop the server
   */
  stop() {
    this.server.close();
    this.wss.close();
  }
}

// Start server if this file is run directly
if (require.main === module) {
  const port = process.env.PORT || 3001;
  const server = new IonLogServer(port);
  server.start();

  // Graceful shutdown
  process.on('SIGINT', () => {
    console.log('\nShutting down server...');
    server.stop();
    process.exit(0);
  });
}

module.exports = IonLogServer;
