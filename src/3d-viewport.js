/**
 * 3D Robot Model Visualization
 * Handles 3D visualization of robot model and sensor data using Three.js
 */

class Viewport3D {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    this.scene = null;
    this.camera = null;
    this.renderer = null;
    this.controls = null;
    this.robotModel = null;
    this.sensorData = new Map();
    this.animationId = null;

    this.initialize();
  }

  /**
   * Initialize the 3D viewport
   */
  initialize() {
    if (typeof THREE === 'undefined') {
      this.showError('Three.js library not loaded');
      return;
    }

    this.setupScene();
    this.setupCamera();
    this.setupRenderer();
    this.setupControls();
    this.setupLighting();
    this.createRobotModel();
    this.setupEventListeners();
    this.animate();
  }

  /**
   * Setup the 3D scene
   */
  setupScene() {
    this.scene = new THREE.Scene();
    this.scene.background = new THREE.Color(0x222222);

    // Add grid
    const gridHelper = new THREE.GridHelper(10, 10, 0x444444, 0x444444);
    this.scene.add(gridHelper);

    // Add axes helper
    const axesHelper = new THREE.AxesHelper(2);
    this.scene.add(axesHelper);
  }

  /**
   * Setup the camera
   */
  setupCamera() {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;

    this.camera = new THREE.PerspectiveCamera(75, width / height, 0.1, 1000);
    this.camera.position.set(5, 5, 5);
    this.camera.lookAt(0, 0, 0);
  }

  /**
   * Setup the renderer
   */
  setupRenderer() {
    this.renderer = new THREE.WebGLRenderer({ antialias: true });
    this.renderer.setSize(this.container.clientWidth, this.container.clientHeight);
    this.renderer.shadowMap.enabled = true;
    this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;

    this.container.appendChild(this.renderer.domElement);
  }

  /**
   * Setup camera controls
   */
  setupControls() {
    if (typeof THREE.OrbitControls !== 'undefined') {
      this.controls = new THREE.OrbitControls(this.camera, this.renderer.domElement);
      this.controls.enableDamping = true;
      this.controls.dampingFactor = 0.05;
    }
  }

  /**
   * Setup lighting
   */
  setupLighting() {
    // Ambient light
    const ambientLight = new THREE.AmbientLight(0x404040, 0.6);
    this.scene.add(ambientLight);

    // Directional light
    const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
    directionalLight.position.set(10, 10, 5);
    directionalLight.castShadow = true;
    directionalLight.shadow.mapSize.width = 2048;
    directionalLight.shadow.mapSize.height = 2048;
    this.scene.add(directionalLight);
  }

  /**
   * Create a simple robot model
   */
  createRobotModel() {
    this.robotModel = new THREE.Group();

    // Robot base (cylinder)
    const baseGeometry = new THREE.CylinderGeometry(0.5, 0.5, 0.2, 16);
    const baseMaterial = new THREE.MeshLambertMaterial({ color: 0x0066cc });
    const base = new THREE.Mesh(baseGeometry, baseMaterial);
    base.position.y = 0.1;
    base.castShadow = true;
    this.robotModel.add(base);

    // Robot body (box)
    const bodyGeometry = new THREE.BoxGeometry(0.6, 0.8, 0.4);
    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x666666 });
    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);
    body.position.y = 0.6;
    body.castShadow = true;
    this.robotModel.add(body);

    // Robot head (sphere)
    const headGeometry = new THREE.SphereGeometry(0.2, 16, 16);
    const headMaterial = new THREE.MeshLambertMaterial({ color: 0x999999 });
    const head = new THREE.Mesh(headGeometry, headMaterial);
    head.position.y = 1.2;
    head.castShadow = true;
    this.robotModel.add(head);

    // Add direction indicator (arrow)
    const arrowGeometry = new THREE.ConeGeometry(0.1, 0.3, 8);
    const arrowMaterial = new THREE.MeshLambertMaterial({ color: 0xff0000 });
    const arrow = new THREE.Mesh(arrowGeometry, arrowMaterial);
    arrow.position.set(0, 0.6, 0.4);
    arrow.rotation.x = Math.PI / 2;
    this.robotModel.add(arrow);

    this.scene.add(this.robotModel);
  }

  /**
   * Update robot pose
   * @param {Object} poseData - Pose data with position and orientation
   */
  updateRobotPose(poseData) {
    if (!this.robotModel || !poseData.data) return;

    const pose = poseData.data;

    // Update position
    if (pose.position) {
      this.robotModel.position.set(
        pose.position.x || 0,
        pose.position.y || 0,
        pose.position.z || 0
      );
    }

    // Update orientation (quaternion)
    if (pose.orientation) {
      const quaternion = new THREE.Quaternion(
        pose.orientation.x || 0,
        pose.orientation.y || 0,
        pose.orientation.z || 0,
        pose.orientation.w || 1
      );
      this.robotModel.setRotationFromQuaternion(quaternion);
    }
  }

  /**
   * Update joint states
   * @param {Object} jointData - Joint state data
   */
  updateJointStates(jointData) {
    if (!jointData.data || !jointData.data.name || !jointData.data.position) return;

    const { name, position, velocity, effort } = jointData.data;

    // Store joint states for visualization
    if (!this.jointStates) {
      this.jointStates = new Map();
    }

    for (let i = 0; i < name.length; i++) {
      this.jointStates.set(name[i], {
        position: position[i] || 0,
        velocity: velocity ? velocity[i] || 0 : 0,
        effort: effort ? effort[i] || 0 : 0
      });
    }

    // Update robot model based on joint states
    this.updateRobotJoints();
  }

  /**
   * Update robot joints based on joint states
   */
  updateRobotJoints() {
    if (!this.robotModel || !this.jointStates) return;

    // Simple joint visualization - rotate parts based on joint positions
    this.jointStates.forEach((state, jointName) => {
      const jointPart = this.robotModel.getObjectByName(jointName);
      if (jointPart) {
        // Rotate joint based on position (simplified)
        jointPart.rotation.z = state.position;
      }
    });
  }

  /**
   * Update LIDAR data visualization
   * @param {Object} lidarData - LIDAR sensor data
   */
  updateLidarData(lidarData) {
    if (!lidarData.data || !lidarData.data.ranges) return;

    // Remove existing LIDAR visualization
    const existingLidar = this.scene.getObjectByName('lidar_points');
    if (existingLidar) {
      this.scene.remove(existingLidar);
    }

    const ranges = lidarData.data.ranges;
    const angleMin = lidarData.data.angle_min || -Math.PI;
    const angleMax = lidarData.data.angle_max || Math.PI;
    const angleIncrement = lidarData.data.angle_increment ||
      (angleMax - angleMin) / ranges.length;

    // Create point cloud for LIDAR data
    const geometry = new THREE.BufferGeometry();
    const positions = [];
    const colors = [];

    for (let i = 0; i < ranges.length; i++) {
      const range = ranges[i];
      if (range > 0 && range < 100) { // Filter invalid readings
        const angle = angleMin + i * angleIncrement;
        const x = range * Math.cos(angle);
        const y = 0.5; // Height of LIDAR sensor
        const z = range * Math.sin(angle);

        positions.push(x, y, z);

        // Color based on distance (closer = red, farther = green)
        const intensity = Math.min(range / 10, 1);
        colors.push(1 - intensity, intensity, 0);
      }
    }

    geometry.setAttribute('position', new THREE.Float32BufferAttribute(positions, 3));
    geometry.setAttribute('color', new THREE.Float32BufferAttribute(colors, 3));

    const material = new THREE.PointsMaterial({
      size: 0.05,
      vertexColors: true
    });

    const points = new THREE.Points(geometry, material);
    points.name = 'lidar_points';
    this.scene.add(points);
  }

  /**
   * Add a trajectory point
   * @param {Object} poseData - Pose data for trajectory
   */
  addTrajectoryPoint(poseData) {
    if (!poseData.data || !poseData.data.position) return;

    let trajectory = this.scene.getObjectByName('trajectory');
    if (!trajectory) {
      const geometry = new THREE.BufferGeometry();
      const material = new THREE.LineBasicMaterial({ color: 0x00ff00 });
      trajectory = new THREE.Line(geometry, material);
      trajectory.name = 'trajectory';
      this.scene.add(trajectory);
    }

    const positions = trajectory.geometry.attributes.position?.array || [];
    const newPositions = new Float32Array([
      ...positions,
      poseData.data.position.x || 0,
      poseData.data.position.y || 0,
      poseData.data.position.z || 0
    ]);

    trajectory.geometry.setAttribute('position',
      new THREE.BufferAttribute(newPositions, 3));
    trajectory.geometry.setDrawRange(0, newPositions.length / 3);
  }

  /**
   * Clear trajectory
   */
  clearTrajectory() {
    const trajectory = this.scene.getObjectByName('trajectory');
    if (trajectory) {
      this.scene.remove(trajectory);
    }
  }

  /**
   * Setup event listeners
   */
  setupEventListeners() {
    // Handle window resize
    window.addEventListener('resize', () => this.onWindowResize());

    // Handle container resize
    const resizeObserver = new ResizeObserver(() => this.onWindowResize());
    resizeObserver.observe(this.container);
  }

  /**
   * Handle window/container resize
   */
  onWindowResize() {
    if (!this.camera || !this.renderer) return;

    const width = this.container.clientWidth;
    const height = this.container.clientHeight;

    this.camera.aspect = width / height;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(width, height);
  }

  /**
   * Animation loop
   */
  animate() {
    this.animationId = requestAnimationFrame(() => this.animate());

    if (this.controls) {
      this.controls.update();
    }

    if (this.renderer && this.scene && this.camera) {
      this.renderer.render(this.scene, this.camera);
    }
  }

  /**
   * Show error message
   * @param {string} message - Error message to display
   */
  showError(message) {
    this.container.innerHTML = `
      <div class="viewport-error">
        <div class="error-icon">⚠️</div>
        <div class="error-message">${message}</div>
        <div class="error-details">Make sure Three.js library is loaded</div>
      </div>
    `;
  }

  /**
   * Reset camera to default position
   */
  resetCamera() {
    if (this.camera) {
      this.camera.position.set(5, 5, 5);
      this.camera.lookAt(0, 0, 0);
    }
    if (this.controls) {
      this.controls.reset();
    }
  }

  /**
   * Dispose of resources
   */
  dispose() {
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }
    if (this.renderer) {
      this.renderer.dispose();
    }
    if (this.controls) {
      this.controls.dispose();
    }
  }
}

// Export for Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = Viewport3D;
}
