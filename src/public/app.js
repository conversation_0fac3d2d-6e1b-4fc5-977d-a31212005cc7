/**
 * Ion Log Playback Frontend Application
 * Main application logic for the web interface
 */

class IonLogApp {
  constructor() {
    this.ws = null;
    this.isConnected = false;
    this.currentState = null;

    console.log('Initializing Ion Log App...');

    // Initialize components
    try {
      console.log('Initializing LogConsole...');
      this.logConsole = new LogConsole('log-console');
      console.log('LogConsole initialized successfully');

      console.log('Initializing CameraView...');
      this.cameraView = new CameraView('camera-view');
      console.log('CameraView initialized successfully');

      console.log('Initializing Viewport3D...');
      this.viewport3D = new Viewport3D('3d-viewport');
      console.log('Viewport3D initialized successfully');
    } catch (error) {
      console.error('Error initializing components:', error);
    }

    this.initializeUI();
    this.connectWebSocket();
  }

  /**
   * Initialize UI event listeners
   */
  initializeUI() {
    console.log('Initializing UI...');

    // File upload
    const fileInput = document.getElementById('file-input');
    const loadFileBtn = document.getElementById('load-file-btn');

    console.log('File input element:', fileInput);
    console.log('Load file button element:', loadFileBtn);

    if (loadFileBtn && fileInput) {
      loadFileBtn.addEventListener('click', () => {
        console.log('Load file button clicked');
        fileInput.click();
      });
      fileInput.addEventListener('change', (e) => {
        console.log('File input changed:', e.target.files);
        this.handleFileUpload(e);
      });
      console.log('File upload event listeners added');
    } else {
      console.error('File upload elements not found!');
    }

    // Playback controls
    document.getElementById('play-btn').addEventListener('click', () => this.playback('play'));
    document.getElementById('pause-btn').addEventListener('click', () => this.playback('pause'));
    document.getElementById('stop-btn').addEventListener('click', () => this.playback('stop'));

    // Timeline control
    const timelineSlider = document.getElementById('timeline-slider');
    timelineSlider.addEventListener('input', (e) => this.seekTo(parseFloat(e.target.value)));

    // Speed control
    const speedSlider = document.getElementById('speed-slider');
    speedSlider.addEventListener('input', (e) => this.setSpeed(parseFloat(e.target.value)));
  }

  /**
   * Connect to WebSocket server
   */
  connectWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}`;

    try {
      this.ws = new WebSocket(wsUrl);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.updateConnectionStatus(true);
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          this.handleWebSocketMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.isConnected = false;
        this.updateConnectionStatus(false);

        // Attempt to reconnect after 3 seconds
        setTimeout(() => this.connectWebSocket(), 3000);
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.updateConnectionStatus(false);
      };

    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      this.updateConnectionStatus(false);
    }
  }

  /**
   * Handle WebSocket messages
   * @param {Object} message - Received message
   */
  handleWebSocketMessage(message) {
    switch (message.type) {
      case 'file_loaded':
        this.handleFileLoaded(message.data);
        break;
      case 'topic_data':
        this.handleTopicData(message.topic, message.data);
        break;
      case 'playback_state_changed':
        this.handlePlaybackStateChanged(message.data);
        break;
      case 'time_changed':
        this.handleTimeChanged(message.data);
        break;
      case 'speed_changed':
        this.handleSpeedChanged(message.data);
        break;
      case 'state_update':
        this.handleStateUpdate(message.data);
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }

  /**
   * Handle file upload
   * @param {Event} event - File input change event
   */
  async handleFileUpload(event) {
    const file = event.target.files[0];
    if (!file) return;

    const formData = new FormData();
    formData.append('ionFile', file);

    try {
      this.updateStatus('Uploading file...');

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      });

      const result = await response.json();

      if (result.success) {
        this.updateStatus(`File loaded: ${file.name}`);
        console.log('File uploaded successfully:', result.data);
      } else {
        throw new Error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      this.updateStatus(`Upload failed: ${error.message}`);
      alert(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Handle file loaded event
   * @param {Object} data - File data
   */
  handleFileLoaded(data) {
    console.log('File loaded:', data);

    // Update topics list
    this.updateTopicsList(data.topics);

    // Update stats
    document.getElementById('topic-count').textContent = data.topics.length;
    document.getElementById('entry-count').textContent = data.totalEntries;

    // Enable controls
    this.enablePlaybackControls(true);

    // Clear previous data
    this.logConsole.clearLogs();
    this.cameraView.clearFrames();

    this.updateStatus('File loaded successfully');
  }

  /**
   * Handle topic data
   * @param {string} topic - Topic name
   * @param {Array} data - Topic data entries
   */
  handleTopicData(topic, data) {
    data.forEach(entry => {
      switch (topic) {
        case '/console/log':
          this.logConsole.addLog(entry);
          break;
        case '/camera/image':
          this.cameraView.displayFrame(entry);
          break;
        case '/robot/pose':
          this.viewport3D.updateRobotPose(entry);
          this.viewport3D.addTrajectoryPoint(entry);
          break;
        case '/sensors/lidar':
          this.viewport3D.updateLidarData(entry);
          break;
        case '/joint_states':
          this.viewport3D.updateJointStates(entry);
          break;
        default:
          // Handle other topics generically
          console.log(`Received data for topic ${topic}:`, entry);
      }
    });
  }

  /**
   * Handle playback state changes
   * @param {Object} state - Playback state
   */
  handlePlaybackStateChanged(state) {
    const playBtn = document.getElementById('play-btn');
    const pauseBtn = document.getElementById('pause-btn');

    if (state.isPlaying) {
      playBtn.textContent = '⏸ Playing';
      playBtn.disabled = true;
      pauseBtn.disabled = false;
      this.updateStatus('Playing');
    } else {
      playBtn.textContent = '▶ Play';
      playBtn.disabled = false;
      pauseBtn.disabled = true;
      this.updateStatus('Paused');
    }
  }

  /**
   * Handle time changes
   * @param {Object} data - Time data
   */
  handleTimeChanged(data) {
    if (this.currentState) {
      const progress = this.currentState.duration > 0 ?
        (data.currentTime - this.currentState.startTime) / this.currentState.duration * 100 : 0;

      document.getElementById('timeline-slider').value = progress;
      document.getElementById('current-time').textContent = this.formatTime(data.currentTime);
    }
  }

  /**
   * Handle speed changes
   * @param {Object} data - Speed data
   */
  handleSpeedChanged(data) {
    document.getElementById('speed-slider').value = data.speed;
    document.getElementById('speed-display').textContent = `${data.speed.toFixed(1)}x`;
  }

  /**
   * Handle state updates
   * @param {Object} state - Current state
   */
  handleStateUpdate(state) {
    this.currentState = state;

    if (state.duration > 0) {
      document.getElementById('total-time').textContent = this.formatTime(state.startTime + state.duration);
      document.getElementById('current-time').textContent = this.formatTime(state.currentTime);

      const progress = (state.currentTime - state.startTime) / state.duration * 100;
      document.getElementById('timeline-slider').value = progress;
    }

    document.getElementById('speed-slider').value = state.playbackSpeed;
    document.getElementById('speed-display').textContent = `${state.playbackSpeed.toFixed(1)}x`;
  }

  /**
   * Control playback
   * @param {string} action - Playback action (play, pause, stop)
   */
  async playback(action) {
    try {
      const response = await fetch(`/api/playback/${action}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error(`Playback ${action} error:`, error);
      alert(`Failed to ${action}: ${error.message}`);
    }
  }

  /**
   * Seek to specific time
   * @param {number} progress - Progress percentage (0-100)
   */
  async seekTo(progress) {
    if (!this.currentState || this.currentState.duration <= 0) return;

    const timestamp = this.currentState.startTime + (progress / 100) * this.currentState.duration;

    try {
      const response = await fetch('/api/playback/seek', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ timestamp })
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Seek error:', error);
    }
  }

  /**
   * Set playback speed
   * @param {number} speed - Playback speed
   */
  async setSpeed(speed) {
    try {
      const response = await fetch('/api/playback/speed', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ speed })
      });

      const result = await response.json();
      if (!result.success) {
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Speed change error:', error);
    }
  }

  /**
   * Update topics list
   * @param {Array} topics - List of topic names
   */
  updateTopicsList(topics) {
    const topicsList = document.getElementById('topics-list');

    if (topics.length === 0) {
      topicsList.innerHTML = '<div class="no-topics">No topics available</div>';
      return;
    }

    topicsList.innerHTML = topics.map(topic => `
      <div class="topic-item" data-topic="${topic}">
        <div class="topic-name">${topic}</div>
        <div class="topic-count">Loading...</div>
      </div>
    `).join('');
  }

  /**
   * Enable/disable playback controls
   * @param {boolean} enabled - Whether to enable controls
   */
  enablePlaybackControls(enabled) {
    document.getElementById('play-btn').disabled = !enabled;
    document.getElementById('pause-btn').disabled = !enabled;
    document.getElementById('stop-btn').disabled = !enabled;
    document.getElementById('timeline-slider').disabled = !enabled;
    document.getElementById('speed-slider').disabled = !enabled;
  }

  /**
   * Update connection status
   * @param {boolean} connected - Connection status
   */
  updateConnectionStatus(connected) {
    const statusElement = document.getElementById('connection-status');
    statusElement.textContent = connected ? 'Connected' : 'Disconnected';
    statusElement.className = `connection-status ${connected ? 'connected' : 'disconnected'}`;
  }

  /**
   * Update status message
   * @param {string} message - Status message
   */
  updateStatus(message) {
    document.getElementById('file-status').textContent = message;
  }

  /**
   * Format timestamp to readable time
   * @param {number} timestamp - Unix timestamp
   * @returns {string} Formatted time string
   */
  formatTime(timestamp) {
    const date = new Date(timestamp * 1000);
    return date.toLocaleTimeString();
  }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  window.ionLogApp = new IonLogApp();
});
