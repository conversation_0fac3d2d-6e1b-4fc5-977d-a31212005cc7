/**
 * Camera Frame Display
 * Handles visualization of camera frames from log data
 */

class CameraView {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    this.currentFrame = null;
    this.frameHistory = [];
    this.maxHistory = 100;
    this.isFullscreen = false;

    this.initializeUI();
  }

  /**
   * Initialize the camera view UI
   */
  initializeUI() {
    this.container.innerHTML = `
      <div class="camera-view">
        <div class="camera-controls">
          <div class="camera-info">
            <span id="camera-resolution">No frame</span>
            <span id="camera-timestamp"></span>
          </div>
          <div class="camera-buttons">
            <button id="save-frame" disabled>Save Frame</button>
            <button id="fullscreen-toggle">Fullscreen</button>
            <button id="fit-to-container">Fit to Container</button>
          </div>
        </div>
        <div class="camera-display" id="camera-display">
          <div class="camera-placeholder">
            <div class="placeholder-icon">📷</div>
            <div class="placeholder-text">No camera frame available</div>
          </div>
        </div>
        <div class="camera-timeline" id="camera-timeline">
          <div class="timeline-track">
            <div class="timeline-progress" id="timeline-progress"></div>
          </div>
          <div class="timeline-controls">
            <button id="prev-frame" disabled>◀ Prev</button>
            <span id="frame-counter">0 / 0</span>
            <button id="next-frame" disabled>Next ▶</button>
          </div>
        </div>
      </div>
    `;

    this.setupEventListeners();
  }

  /**
   * Setup event listeners for camera controls
   */
  setupEventListeners() {
    const saveFrameBtn = document.getElementById('save-frame');
    const fullscreenBtn = document.getElementById('fullscreen-toggle');
    const fitBtn = document.getElementById('fit-to-container');
    const prevBtn = document.getElementById('prev-frame');
    const nextBtn = document.getElementById('next-frame');
    const display = document.getElementById('camera-display');

    saveFrameBtn.addEventListener('click', () => this.saveCurrentFrame());
    fullscreenBtn.addEventListener('click', () => this.toggleFullscreen());
    fitBtn.addEventListener('click', () => this.fitToContainer());
    prevBtn.addEventListener('click', () => this.showPreviousFrame());
    nextBtn.addEventListener('click', () => this.showNextFrame());

    // Handle fullscreen changes
    document.addEventListener('fullscreenchange', () => {
      this.isFullscreen = !!document.fullscreenElement;
      fullscreenBtn.textContent = this.isFullscreen ? 'Exit Fullscreen' : 'Fullscreen';
    });

    // Handle image click for fullscreen
    display.addEventListener('click', (e) => {
      if (e.target.tagName === 'IMG') {
        this.toggleFullscreen();
      }
    });
  }

  /**
   * Display a camera frame
   * @param {Object} frameData - Camera frame data
   */
  displayFrame(frameData) {
    if (!frameData || !frameData.data) return;

    const frame = {
      timestamp: frameData.timestamp,
      width: frameData.data.width || 640,
      height: frameData.data.height || 480,
      encoding: frameData.data.encoding || 'rgb8',
      format: frameData.data.format || 'raw',
      data: frameData.data.data,
      id: Date.now() + Math.random()
    };

    this.currentFrame = frame;
    this.addToHistory(frame);
    this.renderFrame(frame);
    this.updateControls();
  }

  /**
   * Add frame to history
   * @param {Object} frame - Frame to add
   */
  addToHistory(frame) {
    this.frameHistory.push(frame);

    // Limit history size
    if (this.frameHistory.length > this.maxHistory) {
      this.frameHistory = this.frameHistory.slice(-this.maxHistory);
    }
  }

  /**
   * Render a frame to the display
   * @param {Object} frame - Frame to render
   */
  renderFrame(frame) {
    const display = document.getElementById('camera-display');

    if (!frame || !frame.data) {
      this.showPlaceholder();
      return;
    }

    // Create image element
    const img = document.createElement('img');
    img.className = 'camera-frame';
    img.alt = 'Camera Frame';

    // Handle different data formats
    if (frame.data.startsWith('data:')) {
      // Already a data URL
      img.src = frame.data;
    } else if (frame.data.startsWith('base64:')) {
      // Base64 with prefix
      img.src = this.createDataUrl(frame.data.substring(7), frame);
    } else if (typeof frame.data === 'string') {
      // Assume raw base64
      img.src = this.createDataUrl(frame.data, frame);
    } else if (frame.data instanceof Uint8Array || Array.isArray(frame.data)) {
      // Handle binary data (Ion BLOB/CLOB)
      const base64 = this.arrayToBase64(frame.data);
      img.src = this.createDataUrl(base64, frame);
    } else {
      this.showPlaceholder('Unsupported image format');
      return;
    }

    img.onload = () => {
      display.innerHTML = '';
      display.appendChild(img);
      this.updateFrameInfo(frame);
    };

    img.onerror = () => {
      console.error('Failed to load camera frame');
      this.showPlaceholder('Failed to load frame');
    };
  }

  /**
   * Create data URL based on frame format
   * @param {string} base64Data - Base64 encoded data
   * @param {Object} frame - Frame metadata
   * @returns {string} Data URL
   */
  createDataUrl(base64Data, frame) {
    // Determine MIME type based on format and encoding
    let mimeType = 'image/jpeg'; // default

    if (frame.format === 'CompressedImage') {
      // ROS CompressedImage format - usually JPEG
      mimeType = 'image/jpeg';
    } else if (frame.encoding) {
      switch (frame.encoding.toLowerCase()) {
        case 'rgb8':
        case 'bgr8':
          mimeType = 'image/png'; // Raw RGB data, convert to PNG
          break;
        case 'jpeg':
          mimeType = 'image/jpeg';
          break;
        case 'png':
          mimeType = 'image/png';
          break;
        default:
          mimeType = 'image/jpeg';
      }
    }

    return `data:${mimeType};base64,${base64Data}`;
  }

  /**
   * Convert array to base64
   * @param {Uint8Array|Array} array - Binary data array
   * @returns {string} Base64 string
   */
  arrayToBase64(array) {
    if (array instanceof Uint8Array) {
      return btoa(String.fromCharCode.apply(null, array));
    } else if (Array.isArray(array)) {
      return btoa(String.fromCharCode.apply(null, new Uint8Array(array)));
    }
    return '';
  }

  /**
   * Show placeholder when no frame is available
   * @param {string} message - Optional message to display
   */
  showPlaceholder(message = 'No camera frame available') {
    const display = document.getElementById('camera-display');
    display.innerHTML = `
      <div class="camera-placeholder">
        <div class="placeholder-icon">📷</div>
        <div class="placeholder-text">${message}</div>
      </div>
    `;
    this.updateFrameInfo(null);
  }

  /**
   * Update frame information display
   * @param {Object} frame - Current frame
   */
  updateFrameInfo(frame) {
    const resolutionSpan = document.getElementById('camera-resolution');
    const timestampSpan = document.getElementById('camera-timestamp');

    if (frame) {
      resolutionSpan.textContent = `${frame.width} × ${frame.height} (${frame.encoding})`;
      timestampSpan.textContent = new Date(frame.timestamp * 1000).toLocaleTimeString();
    } else {
      resolutionSpan.textContent = 'No frame';
      timestampSpan.textContent = '';
    }
  }

  /**
   * Update control states
   */
  updateControls() {
    const saveBtn = document.getElementById('save-frame');
    const prevBtn = document.getElementById('prev-frame');
    const nextBtn = document.getElementById('next-frame');
    const counter = document.getElementById('frame-counter');

    const hasFrame = !!this.currentFrame;
    const currentIndex = this.frameHistory.findIndex(f => f.id === this.currentFrame?.id);

    saveBtn.disabled = !hasFrame;
    prevBtn.disabled = currentIndex <= 0;
    nextBtn.disabled = currentIndex >= this.frameHistory.length - 1 || currentIndex === -1;

    if (hasFrame && currentIndex !== -1) {
      counter.textContent = `${currentIndex + 1} / ${this.frameHistory.length}`;
    } else {
      counter.textContent = `0 / ${this.frameHistory.length}`;
    }
  }

  /**
   * Show previous frame from history
   */
  showPreviousFrame() {
    const currentIndex = this.frameHistory.findIndex(f => f.id === this.currentFrame?.id);
    if (currentIndex > 0) {
      const prevFrame = this.frameHistory[currentIndex - 1];
      this.currentFrame = prevFrame;
      this.renderFrame(prevFrame);
      this.updateControls();
    }
  }

  /**
   * Show next frame from history
   */
  showNextFrame() {
    const currentIndex = this.frameHistory.findIndex(f => f.id === this.currentFrame?.id);
    if (currentIndex < this.frameHistory.length - 1 && currentIndex !== -1) {
      const nextFrame = this.frameHistory[currentIndex + 1];
      this.currentFrame = nextFrame;
      this.renderFrame(nextFrame);
      this.updateControls();
    }
  }

  /**
   * Save current frame as image
   */
  saveCurrentFrame() {
    if (!this.currentFrame) return;

    const img = document.querySelector('.camera-frame');
    if (!img) return;

    // Create download link
    const link = document.createElement('a');
    link.download = `camera_frame_${this.currentFrame.timestamp}.png`;
    link.href = img.src;
    link.click();
  }

  /**
   * Toggle fullscreen mode
   */
  toggleFullscreen() {
    const display = document.getElementById('camera-display');

    if (!this.isFullscreen) {
      if (display.requestFullscreen) {
        display.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  }

  /**
   * Fit image to container
   */
  fitToContainer() {
    const img = document.querySelector('.camera-frame');
    if (img) {
      img.style.maxWidth = '100%';
      img.style.maxHeight = '100%';
      img.style.objectFit = 'contain';
    }
  }

  /**
   * Clear all frames
   */
  clearFrames() {
    this.currentFrame = null;
    this.frameHistory = [];
    this.showPlaceholder();
    this.updateControls();
  }

  /**
   * Get frame statistics
   * @returns {Object} Frame statistics
   */
  getStats() {
    return {
      totalFrames: this.frameHistory.length,
      currentFrame: this.currentFrame ? this.frameHistory.findIndex(f => f.id === this.currentFrame.id) + 1 : 0,
      hasCurrentFrame: !!this.currentFrame
    };
  }
}

// Export for Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CameraView;
}
