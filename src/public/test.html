<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Upload Test</title>
</head>
<body>
    <h1>File Upload Test</h1>
    <input type="file" id="file-input" accept=".ion" style="display: none;">
    <button id="load-file-btn">Load Ion File</button>
    <div id="status">No file selected</div>

    <script>
        console.log('Test page loaded');
        
        const fileInput = document.getElementById('file-input');
        const loadFileBtn = document.getElementById('load-file-btn');
        const status = document.getElementById('status');
        
        console.log('File input:', fileInput);
        console.log('Load button:', loadFileBtn);
        
        loadFileBtn.addEventListener('click', () => {
            console.log('Button clicked');
            fileInput.click();
        });
        
        fileInput.addEventListener('change', (e) => {
            console.log('File selected:', e.target.files);
            if (e.target.files.length > 0) {
                status.textContent = `Selected: ${e.target.files[0].name}`;
                
                // Test upload
                const formData = new FormData();
                formData.append('ionFile', e.target.files[0]);
                
                fetch('/api/upload', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(result => {
                    console.log('Upload result:', result);
                    status.textContent = result.success ? 'Upload successful!' : 'Upload failed: ' + result.error;
                })
                .catch(error => {
                    console.error('Upload error:', error);
                    status.textContent = 'Upload error: ' + error.message;
                });
            }
        });
    </script>
</body>
</html>
