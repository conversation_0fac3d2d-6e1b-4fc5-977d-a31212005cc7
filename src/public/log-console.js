/**
 * Log Console Display with Search
 * Handles console log display, filtering, and search functionality
 */

class LogConsole {
  constructor(containerId) {
    this.container = document.getElementById(containerId);
    if (!this.container) {
      console.error(`LogConsole: Container element with ID '${containerId}' not found`);
      return;
    }

    this.containerId = containerId;
    this.logs = [];
    this.filteredLogs = [];
    this.searchTerm = '';
    this.levelFilter = 'ALL';
    this.moduleFilter = 'ALL';
    this.maxLogs = 1000; // Limit for performance

    // Generate unique IDs for this instance
    this.searchId = `${containerId}-log-search`;
    this.clearSearchId = `${containerId}-clear-search`;
    this.levelFilterId = `${containerId}-level-filter`;
    this.moduleFilterId = `${containerId}-module-filter`;
    this.clearLogsId = `${containerId}-clear-logs`;
    this.displayId = `${containerId}-log-display`;
    this.countId = `${containerId}-log-count`;
    this.filteredCountId = `${containerId}-filtered-count`;

    this.initializeUI();
  }

  /**
   * Initialize the console UI
   */
  initializeUI() {
    this.container.innerHTML = `
      <div class="log-console">
        <div class="log-controls">
          <div class="search-container">
            <input type="text" id="${this.searchId}" placeholder="Search logs..." />
            <button id="${this.clearSearchId}">Clear</button>
          </div>
          <div class="filter-container">
            <select id="${this.levelFilterId}">
              <option value="ALL">All Levels</option>
              <option value="DEBUG">Debug</option>
              <option value="INFO">Info</option>
              <option value="WARN">Warning</option>
              <option value="ERROR">Error</option>
            </select>
            <select id="${this.moduleFilterId}">
              <option value="ALL">All Modules</option>
            </select>
            <button id="${this.clearLogsId}">Clear Logs</button>
          </div>
        </div>
        <div class="log-display" id="${this.displayId}">
          <div class="log-placeholder">No logs to display</div>
        </div>
        <div class="log-stats">
          <span id="${this.countId}">0 logs</span>
          <span id="${this.filteredCountId}"></span>
        </div>
      </div>
    `;

    this.setupEventListeners();
  }

  /**
   * Setup event listeners for controls
   */
  setupEventListeners() {
    const searchInput = document.getElementById(this.searchId);
    const clearSearchBtn = document.getElementById(this.clearSearchId);
    const levelFilter = document.getElementById(this.levelFilterId);
    const moduleFilter = document.getElementById(this.moduleFilterId);
    const clearLogsBtn = document.getElementById(this.clearLogsId);

    searchInput.addEventListener('input', (e) => {
      this.searchTerm = e.target.value.toLowerCase();
      this.applyFilters();
    });

    clearSearchBtn.addEventListener('click', () => {
      searchInput.value = '';
      this.searchTerm = '';
      this.applyFilters();
    });

    levelFilter.addEventListener('change', (e) => {
      this.levelFilter = e.target.value;
      this.applyFilters();
    });

    moduleFilter.addEventListener('change', (e) => {
      this.moduleFilter = e.target.value;
      this.applyFilters();
    });

    clearLogsBtn.addEventListener('click', () => {
      this.clearLogs();
    });
  }

  /**
   * Add a log entry
   * @param {Object} logEntry - Log entry object
   */
  addLog(logEntry) {
    const log = {
      timestamp: logEntry.timestamp || Date.now() / 1000,
      level: logEntry.data?.level || 'INFO',
      message: logEntry.data?.message || logEntry.data || '',
      module: logEntry.data?.module || 'unknown',
      id: Date.now() + Math.random()
    };

    this.logs.push(log);

    // Limit number of logs for performance
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs);
    }

    this.updateModuleFilter();
    this.applyFilters();
  }

  /**
   * Add multiple log entries
   * @param {Array} logEntries - Array of log entries
   */
  addLogs(logEntries) {
    logEntries.forEach(entry => this.addLog(entry));
  }

  /**
   * Apply current filters to logs
   */
  applyFilters() {
    this.filteredLogs = this.logs.filter(log => {
      // Search filter
      if (this.searchTerm && !log.message.toLowerCase().includes(this.searchTerm)) {
        return false;
      }

      // Level filter
      if (this.levelFilter !== 'ALL' && log.level !== this.levelFilter) {
        return false;
      }

      // Module filter
      if (this.moduleFilter !== 'ALL' && log.module !== this.moduleFilter) {
        return false;
      }

      return true;
    });

    this.renderLogs();
    this.updateStats();
  }

  /**
   * Render filtered logs to the display
   */
  renderLogs() {
    const display = document.getElementById(this.displayId);

    if (this.filteredLogs.length === 0) {
      display.innerHTML = '<div class="log-placeholder">No logs match current filters</div>';
      return;
    }

    const logsHtml = this.filteredLogs.map(log => {
      const timestamp = new Date(log.timestamp * 1000).toLocaleTimeString();
      const levelClass = `log-level-${log.level.toLowerCase()}`;

      return `
        <div class="log-entry ${levelClass}">
          <span class="log-timestamp">${timestamp}</span>
          <span class="log-level">[${log.level}]</span>
          <span class="log-module">${log.module}</span>
          <span class="log-message">${this.escapeHtml(log.message)}</span>
        </div>
      `;
    }).join('');

    display.innerHTML = logsHtml;

    // Auto-scroll to bottom
    display.scrollTop = display.scrollHeight;
  }

  /**
   * Update module filter options
   */
  updateModuleFilter() {
    const moduleFilter = document.getElementById(this.moduleFilterId);
    const modules = [...new Set(this.logs.map(log => log.module))].sort();

    // Keep current selection if it exists
    const currentValue = moduleFilter.value;

    moduleFilter.innerHTML = '<option value="ALL">All Modules</option>';
    modules.forEach(module => {
      const option = document.createElement('option');
      option.value = module;
      option.textContent = module;
      moduleFilter.appendChild(option);
    });

    // Restore selection if it still exists
    if (modules.includes(currentValue)) {
      moduleFilter.value = currentValue;
    }
  }

  /**
   * Update statistics display
   */
  updateStats() {
    const logCount = document.getElementById(this.countId);
    const filteredCount = document.getElementById(this.filteredCountId);

    logCount.textContent = `${this.logs.length} total logs`;

    if (this.filteredLogs.length !== this.logs.length) {
      filteredCount.textContent = `(${this.filteredLogs.length} shown)`;
      filteredCount.style.display = 'inline';
    } else {
      filteredCount.style.display = 'none';
    }
  }

  /**
   * Clear all logs
   */
  clearLogs() {
    this.logs = [];
    this.filteredLogs = [];
    this.renderLogs();
    this.updateStats();
    this.updateModuleFilter();
  }

  /**
   * Escape HTML characters
   * @param {string} text - Text to escape
   * @returns {string} Escaped text
   */
  escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
  }

  /**
   * Export logs as JSON
   * @returns {string} JSON string of current filtered logs
   */
  exportLogs() {
    return JSON.stringify(this.filteredLogs, null, 2);
  }

  /**
   * Get current filter state
   * @returns {Object} Current filter configuration
   */
  getFilterState() {
    return {
      searchTerm: this.searchTerm,
      levelFilter: this.levelFilter,
      moduleFilter: this.moduleFilter
    };
  }

  /**
   * Set filter state
   * @param {Object} state - Filter state to apply
   */
  setFilterState(state) {
    if (state.searchTerm !== undefined) {
      this.searchTerm = state.searchTerm;
      document.getElementById(this.searchId).value = state.searchTerm;
    }
    if (state.levelFilter !== undefined) {
      this.levelFilter = state.levelFilter;
      document.getElementById(this.levelFilterId).value = state.levelFilter;
    }
    if (state.moduleFilter !== undefined) {
      this.moduleFilter = state.moduleFilter;
      document.getElementById(this.moduleFilterId).value = state.moduleFilter;
    }
    this.applyFilters();
  }
}

// Export for Node.js environment
if (typeof module !== 'undefined' && module.exports) {
  module.exports = LogConsole;
}
