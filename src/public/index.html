<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ion Log Playback</title>
    <link rel="stylesheet" href="styles.css">
    <!-- Three.js for 3D visualization -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/controls/OrbitControls.js"></script>
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <h1>Ion Log Playback</h1>
            <div class="header-controls">
                <input type="file" id="file-input" accept=".ion" style="display: none;">
                <button id="load-file-btn" class="btn btn-primary">Load Ion File</button>
                <div class="connection-status" id="connection-status">Disconnected</div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <!-- Playback Controls -->
            <section class="playback-section">
                <div class="playback-controls">
                    <button id="play-btn" class="btn" disabled>▶ Play</button>
                    <button id="pause-btn" class="btn" disabled>⏸ Pause</button>
                    <button id="stop-btn" class="btn" disabled>⏹ Stop</button>
                    <div class="timeline-container">
                        <input type="range" id="timeline-slider" min="0" max="100" value="0" disabled>
                        <div class="time-display">
                            <span id="current-time">00:00</span> / <span id="total-time">00:00</span>
                        </div>
                    </div>
                    <div class="speed-control">
                        <label for="speed-slider">Speed:</label>
                        <input type="range" id="speed-slider" min="0.1" max="5" step="0.1" value="1" disabled>
                        <span id="speed-display">1.0x</span>
                    </div>
                </div>
            </section>

            <!-- Content Grid -->
            <div class="content-grid">
                <!-- Log Console -->
                <section class="panel log-panel">
                    <h2>Console Logs</h2>
                    <div id="log-console"></div>
                </section>

                <!-- Camera View -->
                <section class="panel camera-panel">
                    <h2>Camera View</h2>
                    <div id="camera-view"></div>
                </section>

                <!-- 3D Viewport -->
                <section class="panel viewport-panel">
                    <h2>3D Viewport</h2>
                    <div id="3d-viewport"></div>
                </section>

                <!-- Topic List -->
                <section class="panel topics-panel">
                    <h2>Topics</h2>
                    <div class="topics-container">
                        <div class="topics-list" id="topics-list">
                            <div class="no-topics">No topics available</div>
                        </div>
                        <div class="topic-stats" id="topic-stats">
                            <div>Total Topics: <span id="topic-count">0</span></div>
                            <div>Total Entries: <span id="entry-count">0</span></div>
                        </div>
                    </div>
                </section>
            </div>
        </main>

        <!-- Status Bar -->
        <footer class="status-bar">
            <div class="status-item">
                <span id="file-status">No file loaded</span>
            </div>
            <div class="status-item">
                <span id="playback-status">Stopped</span>
            </div>
            <div class="status-item">
                <span id="data-status">No data</span>
            </div>
        </footer>
    </div>

    <!-- Load application scripts -->
    <script src="log-console.js"></script>
    <script src="camera-view.js"></script>
    <script src="3d-viewport.js"></script>
    <script src="app.js"></script>
</body>
</html>
