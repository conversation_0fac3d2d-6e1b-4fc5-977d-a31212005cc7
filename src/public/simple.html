<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ion Log Playback - Simple</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="app-container">
        <!-- Header -->
        <header class="app-header">
            <h1>Ion Log Playback</h1>
            <div class="header-controls">
                <input type="file" id="file-input" accept=".ion" style="display: none;">
                <button id="load-file-btn" class="btn btn-primary">Load Ion File</button>
                <div class="connection-status" id="connection-status">Disconnected</div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="app-main">
            <div style="padding: 20px;">
                <h2>File Upload Test</h2>
                <div id="status">No file loaded</div>
                <div id="topics-list">No topics</div>
            </div>
        </main>
    </div>

    <script>
        console.log('Simple page loaded');
        
        // Simple file upload handler
        class SimpleApp {
            constructor() {
                this.initializeUI();
            }
            
            initializeUI() {
                console.log('Initializing UI...');
                
                const fileInput = document.getElementById('file-input');
                const loadFileBtn = document.getElementById('load-file-btn');
                const status = document.getElementById('status');
                const topicsList = document.getElementById('topics-list');
                
                console.log('File input:', fileInput);
                console.log('Load button:', loadFileBtn);
                
                if (loadFileBtn && fileInput) {
                    loadFileBtn.addEventListener('click', () => {
                        console.log('Load file button clicked');
                        fileInput.click();
                    });
                    
                    fileInput.addEventListener('change', async (e) => {
                        console.log('File input changed:', e.target.files);
                        if (e.target.files.length > 0) {
                            const file = e.target.files[0];
                            status.textContent = `Uploading: ${file.name}...`;
                            
                            try {
                                const formData = new FormData();
                                formData.append('ionFile', file);
                                
                                const response = await fetch('/api/upload', {
                                    method: 'POST',
                                    body: formData
                                });
                                
                                const result = await response.json();
                                console.log('Upload result:', result);
                                
                                if (result.success) {
                                    status.textContent = `File loaded: ${file.name}`;
                                    topicsList.innerHTML = `
                                        <h3>Topics (${result.data.topics.length}):</h3>
                                        <ul>
                                            ${result.data.topics.map(topic => `<li>${topic}</li>`).join('')}
                                        </ul>
                                        <p>Total entries: ${result.data.totalEntries}</p>
                                        <p>Time range: ${result.data.timeRange.min} - ${result.data.timeRange.max}</p>
                                    `;
                                } else {
                                    status.textContent = `Upload failed: ${result.error}`;
                                }
                            } catch (error) {
                                console.error('Upload error:', error);
                                status.textContent = `Upload error: ${error.message}`;
                            }
                        }
                    });
                    
                    console.log('Event listeners added successfully');
                } else {
                    console.error('Required elements not found!');
                }
            }
        }
        
        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            console.log('DOM loaded, initializing app...');
            window.simpleApp = new SimpleApp();
        });
    </script>
</body>
</html>
