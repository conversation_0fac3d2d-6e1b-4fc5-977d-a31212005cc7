/**
 * Ion File Parser
 * Handles parsing of Ion format log files and extraction of topics
 */

const fs = require('fs');
const path = require('path');
const ion = require('ion-js');

class IonParser {
  constructor() {
    this.topics = new Map();
    this.entries = [];
  }

  /**
   * Parse an Ion file and extract all entries
   * @param {string} filePath - Path to the Ion file
   * @returns {Promise<Object>} Parsed data with topics and entries
   */
  async parseFile(filePath) {
    try {
      const content = fs.readFileSync(filePath);
      return this.parseContent(content);
    } catch (error) {
      throw new Error(`Failed to parse Ion file: ${error.message}`);
    }
  }

  /**
   * Parse Ion content from buffer or string
   * @param {Buffer|string} content - Ion file content
   * @returns {Object} Parsed data with topics and entries
   */
  parseContent(content) {
    this.topics.clear();
    this.entries = [];

    try {
      // Try to parse as Ion binary format first
      if (Buffer.isBuffer(content)) {
        return this.parseIonBinary(content);
      }

      // Try to parse as Ion text format
      if (typeof content === 'string') {
        return this.parseIonText(content);
      }

      throw new Error('Invalid content type');
    } catch (error) {
      // Fallback to custom format parsing
      console.warn('Ion parsing failed, falling back to custom format:', error.message);
      if (Buffer.isBuffer(content)) {
        content = content.toString('utf8');
      }
      return this.parseCustomFormat(content);
    }
  }

  /**
   * Parse Ion binary format
   * @param {Buffer} buffer - Ion binary data
   * @returns {Object} Parsed data
   */
  parseIonBinary(buffer) {
    const reader = ion.makeReader(buffer);
    return this.parseIonData(reader);
  }

  /**
   * Parse Ion text format
   * @param {string} text - Ion text data
   * @returns {Object} Parsed data
   */
  parseIonText(text) {
    const reader = ion.makeReader(text);
    return this.parseIonData(reader);
  }

  /**
   * Parse Ion data using reader
   * @param {IonReader} reader - Ion reader
   * @returns {Object} Parsed data
   */
  parseIonData(reader) {
    let type;
    while ((type = reader.next()) !== null) {
      if (type === ion.IonTypes.STRUCT) {
        const entry = this.parseIonStruct(reader);
        if (entry && entry.timestamp !== undefined && entry.topic) {
          this.addEntry(entry);
        }
      }
    }

    return {
      topics: Array.from(this.topics.keys()),
      entries: this.entries,
      totalEntries: this.entries.length,
      timeRange: this.getTimeRange()
    };
  }

  /**
   * Parse Ion struct into entry object
   * @param {IonReader} reader - Ion reader positioned at struct
   * @returns {Object} Entry object
   */
  parseIonStruct(reader) {
    const entry = {};
    reader.stepIn();

    let type;
    while ((type = reader.next()) !== null) {
      const fieldName = reader.fieldName();

      if (fieldName === 'timestamp') {
        if (type === ion.IonTypes.TIMESTAMP) {
          const timestamp = reader.timestampValue();
          entry.timestamp = timestamp.getSecondsValue ? timestamp.getSecondsValue() :
                           timestamp.secondsValue ? timestamp.secondsValue() :
                           new Date(timestamp.toString()).getTime() / 1000;
        } else if (type === ion.IonTypes.DECIMAL || type === ion.IonTypes.FLOAT) {
          entry.timestamp = reader.numberValue();
        }
      } else if (fieldName === 'topic') {
        if (type === ion.IonTypes.STRING || type === ion.IonTypes.SYMBOL) {
          entry.topic = reader.stringValue();
        }
      } else if (fieldName === 'data') {
        entry.data = this.parseIonValue(reader, type);
      }
    }

    reader.stepOut();
    return entry;
  }

  /**
   * Parse Ion value recursively
   * @param {IonReader} reader - Ion reader
   * @param {IonType} type - Ion type
   * @returns {any} Parsed value
   */
  parseIonValue(reader, type) {
    switch (type) {
      case ion.IonTypes.NULL:
        return null;
      case ion.IonTypes.BOOL:
        return reader.booleanValue();
      case ion.IonTypes.INT:
      case ion.IonTypes.FLOAT:
      case ion.IonTypes.DECIMAL:
        return reader.numberValue();
      case ion.IonTypes.STRING:
      case ion.IonTypes.SYMBOL:
        return reader.stringValue();
      case ion.IonTypes.TIMESTAMP:
        return reader.timestampValue().toString();
      case ion.IonTypes.BLOB:
      case ion.IonTypes.CLOB:
        return reader.byteValue();
      case ion.IonTypes.LIST:
        return this.parseIonList(reader);
      case ion.IonTypes.SEXP:
        return this.parseIonSexp(reader);
      case ion.IonTypes.STRUCT:
        return this.parseIonStructValue(reader);
      default:
        return null;
    }
  }

  /**
   * Parse Ion list
   * @param {IonReader} reader - Ion reader
   * @returns {Array} Parsed array
   */
  parseIonList(reader) {
    const list = [];
    reader.stepIn();

    let type;
    while ((type = reader.next()) !== null) {
      list.push(this.parseIonValue(reader, type));
    }

    reader.stepOut();
    return list;
  }

  /**
   * Parse Ion S-expression
   * @param {IonReader} reader - Ion reader
   * @returns {Array} Parsed array
   */
  parseIonSexp(reader) {
    return this.parseIonList(reader); // Same as list for our purposes
  }

  /**
   * Parse Ion struct value (not entry)
   * @param {IonReader} reader - Ion reader
   * @returns {Object} Parsed object
   */
  parseIonStructValue(reader) {
    const obj = {};
    reader.stepIn();

    let type;
    while ((type = reader.next()) !== null) {
      const fieldName = reader.fieldName();
      obj[fieldName] = this.parseIonValue(reader, type);
    }

    reader.stepOut();
    return obj;
  }

  /**
   * Parse custom format (fallback)
   * @param {string} content - Custom format content
   * @returns {Object} Parsed data
   */
  parseCustomFormat(content) {
    // Try to parse as Ion-like JSON structures first
    try {
      const result = this.parseIonLikeJson(content);
      if (result.totalEntries > 0) {
        return result;
      }
    } catch (error) {
      console.warn('Ion-like JSON parsing failed, trying line-by-line parsing:', error.message);
    }

    // Fallback to line-by-line parsing with multi-line JSON support
    const lines = content.split('\n');
    let currentEntry = {};
    let inDataBlock = false;
    let dataLines = [];
    let braceCount = 0;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip comments and empty lines (but not when in data block)
      if (!inDataBlock && (line.startsWith('#') || line.startsWith('//') || line === '')) {
        continue;
      }

      // Parse timestamp
      if (line.startsWith('timestamp:')) {
        if (Object.keys(currentEntry).length > 0) {
          this.addEntry(currentEntry);
          currentEntry = {};
        }
        currentEntry.timestamp = parseFloat(line.split(':')[1].trim().replace(',', ''));
      }

      // Parse topic
      else if (line.startsWith('topic:')) {
        currentEntry.topic = line.split(':')[1].trim().replace(/[",]/g, '');
      }

      // Parse data (handle multi-line JSON)
      else if (line.startsWith('data:')) {
        inDataBlock = true;
        dataLines = [];
        const dataStart = line.substring(5).trim();

        if (dataStart === '{') {
          braceCount = 1;
          dataLines.push(dataStart);
        } else {
          // Single line data
          try {
            currentEntry.data = this.parseCustomData(dataStart);
            inDataBlock = false;
          } catch (error) {
            console.warn(`Failed to parse single-line data on line ${i + 1}: ${error.message}`);
            currentEntry.data = dataStart;
            inDataBlock = false;
          }
        }
      }

      // Continue collecting data lines
      else if (inDataBlock) {
        dataLines.push(line);

        // Count braces to know when JSON object ends
        for (const char of line) {
          if (char === '{') braceCount++;
          else if (char === '}') braceCount--;
        }

        // If braces are balanced, we have complete JSON
        if (braceCount === 0) {
          const dataStr = dataLines.join('\n');
          try {
            currentEntry.data = JSON.parse(dataStr);
          } catch (error) {
            console.warn(`Failed to parse multi-line data ending at line ${i + 1}: ${error.message}`);
            currentEntry.data = dataStr;
          }
          inDataBlock = false;
          dataLines = [];
        }
      }
    }

    // Add the last entry
    if (Object.keys(currentEntry).length > 0) {
      this.addEntry(currentEntry);
    }

    return {
      topics: Array.from(this.topics.keys()),
      entries: this.entries,
      totalEntries: this.entries.length,
      timeRange: this.getTimeRange()
    };
  }

  /**
   * Parse Ion-like JSON structures
   * @param {string} content - Content with Ion-like JSON structures
   * @returns {Object} Parsed data
   */
  parseIonLikeJson(content) {
    // Split content into individual JSON objects
    const jsonObjects = [];
    let braceCount = 0;
    let currentObject = '';
    let inString = false;
    let escapeNext = false;

    for (let i = 0; i < content.length; i++) {
      const char = content[i];

      if (escapeNext) {
        escapeNext = false;
        currentObject += char;
        continue;
      }

      if (char === '\\') {
        escapeNext = true;
        currentObject += char;
        continue;
      }

      if (char === '"' && !escapeNext) {
        inString = !inString;
      }

      if (!inString) {
        if (char === '{') {
          braceCount++;
        } else if (char === '}') {
          braceCount--;
        }
      }

      currentObject += char;

      if (braceCount === 0 && currentObject.trim() !== '') {
        const trimmed = currentObject.trim();
        if (trimmed.startsWith('{') && trimmed.endsWith('}')) {
          jsonObjects.push(trimmed);
        }
        currentObject = '';
      }
    }

    // Parse each JSON object
    for (const jsonStr of jsonObjects) {
      try {
        // Handle Ion BLOB syntax {{...}}
        const cleanedJson = jsonStr.replace(/\{\{([^}]+)\}\}/g, '"$1"');
        const obj = JSON.parse(cleanedJson);

        if (obj.timestamp !== undefined && obj.topic && obj.data !== undefined) {
          this.addEntry(obj);
        }
      } catch (error) {
        console.warn('Failed to parse JSON object:', error.message, jsonStr.substring(0, 100));
      }
    }

    return {
      topics: Array.from(this.topics.keys()),
      entries: this.entries,
      totalEntries: this.entries.length,
      timeRange: this.getTimeRange()
    };
  }

  /**
   * Parse custom data field (simplified JSON parser)
   * @param {string} dataStr - Data string to parse
   * @returns {Object} Parsed data object
   */
  parseCustomData(dataStr) {
    // Remove outer braces and parse as JSON-like structure
    if (dataStr.startsWith('{') && dataStr.endsWith('}')) {
      return JSON.parse(dataStr);
    }
    return dataStr;
  }

  /**
   * Add an entry to the collection
   * @param {Object} entry - Log entry to add
   */
  addEntry(entry) {
    if (entry.topic && entry.timestamp !== undefined) {
      this.entries.push(entry);

      // Track topics
      if (!this.topics.has(entry.topic)) {
        this.topics.set(entry.topic, []);
      }
      this.topics.get(entry.topic).push(entry);
    }
  }

  /**
   * Get time range of all entries
   * @returns {Object} Time range with min and max timestamps
   */
  getTimeRange() {
    if (this.entries.length === 0) {
      return { min: 0, max: 0 };
    }

    const timestamps = this.entries.map(entry => entry.timestamp);
    return {
      min: Math.min(...timestamps),
      max: Math.max(...timestamps)
    };
  }

  /**
   * Get entries for a specific topic
   * @param {string} topicName - Name of the topic
   * @returns {Array} Array of entries for the topic
   */
  getTopicEntries(topicName) {
    return this.topics.get(topicName) || [];
  }

  /**
   * Get entries within a time range
   * @param {number} startTime - Start timestamp
   * @param {number} endTime - End timestamp
   * @returns {Array} Array of entries within the time range
   */
  getEntriesInTimeRange(startTime, endTime) {
    return this.entries.filter(entry =>
      entry.timestamp >= startTime && entry.timestamp <= endTime
    );
  }
}

module.exports = IonParser;
