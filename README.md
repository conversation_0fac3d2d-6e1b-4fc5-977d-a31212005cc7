# Ion Log Playback

A web-based tool for parsing, visualizing, and playing back Amazon Ion format log files with support for:

- **Ion Format Support**: Full Amazon Ion binary and text format parsing
- **ROS Message Types**: Support for CompressedImage, JointState, and other ROS message formats
- **Real-time Playback**: Timeline control with variable speed playback
- **Console Logs**: Search and filter console logs by level and module
- **Camera Visualization**: Display camera frames with support for various encodings
- **3D Robot Model**: Interactive 3D visualization with joint states and sensor data

## Project Structure

```
ion-log-playback/
├── src/
│   ├── parser.js           # Ion file parsing logic
│   ├── topic-reader.js     # Topic reader and playback control
│   ├── log-console.js      # Console log display with search
│   ├── camera-view.js      # Camera frame display
│   ├── 3d-viewport.js      # 3D robot model visualization
│   ├── server.js           # Express server for web interface
│   └── public/             # Static files (HTML, CSS, JS for frontend)
│       ├── index.html
│       ├── styles.css
│       └── app.js
├── example.ion             # Sample Ion file
├── package.json
├── README.md
└── .gitignore
```

## Installation

```bash
npm install
```

## Usage

```bash
npm start
```

Then open http://localhost:3001 in your browser.

## Development

```bash
npm run dev
```

## Testing

Test the parser with the included example files:
- `example-simple.ion` - Simple format with multi-line JSON data
- `example.ion` - Ion text format (experimental)

## Features

1. **Ion File Parsing** - Parse Amazon Ion binary and text format log files
   - Support for Ion structs, lists, timestamps, and binary data (BLOBs)
   - Automatic fallback to custom format for compatibility
   - Efficient parsing of large log files

2. **Topic Reader** - Real-time playback control with timeline
   - Variable speed playback (0.1x to 10x)
   - Precise seeking and timeline navigation
   - Event-driven topic subscription system

3. **Log Console** - Advanced console log management
   - Search across all log messages
   - Filter by log level (DEBUG, INFO, WARN, ERROR)
   - Filter by module/component
   - Real-time log streaming during playback

4. **Camera View** - Multi-format image visualization
   - Support for ROS CompressedImage format
   - Multiple encoding support (RGB8, BGR8, JPEG, PNG)
   - Frame history navigation and fullscreen viewing
   - Save frame functionality

5. **3D Viewport** - Interactive robot visualization
   - 3D robot model with joint state animation
   - LIDAR point cloud visualization
   - Robot trajectory tracking
   - Real-time pose updates

## Supported Data Formats

### Ion Format
- **Binary Ion**: Efficient binary format for large datasets
- **Text Ion**: Human-readable Ion text format
- **Timestamps**: Native Ion timestamp support with nanosecond precision
- **Binary Data**: BLOB/CLOB support for image and sensor data

### ROS Message Types
- **CompressedImage**: JPEG/PNG compressed image messages
- **JointState**: Robot joint position, velocity, and effort data
- **LaserScan**: LIDAR range data with angle information
- **Pose**: 3D position and orientation (quaternion)

### Log Entry Structure
Each log entry should be an Ion struct with the following fields:
```ion
{
  timestamp: 2023-12-01T10:00:00.123Z,  // Ion timestamp
  topic: "/robot/pose",                  // Topic name (string)
  data: {                               // Message data (struct)
    // Topic-specific data fields
  }
}
```
