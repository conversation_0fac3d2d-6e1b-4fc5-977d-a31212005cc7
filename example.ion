{
  timestamp: 1701424800.123,
  topic: "/robot/pose",
  data: {
    position: { x: 1.0, y: 2.0, z: 0.5 },
    orientation: { x: 0.0, y: 0.0, z: 0.0, w: 1.0 }
  }
}

{
  timestamp: 1701424800.223,
  topic: "/camera/image",
  data: {
    width: 640,
    height: 480,
    encoding: "rgb8",
    format: "CompressedImage",
    data: {{aGVsbG8gd29ybGQ=}}
  }
}

{
  timestamp: 1701424800.323,
  topic: "/console/log",
  data: {
    level: "INFO",
    message: "Robot initialized successfully",
    module: "main"
  }
}

{
  timestamp: 1701424800.423,
  topic: "/sensors/lidar",
  data: {
    ranges: [1.2, 1.5, 2.0, 1.8, 1.1],
    angle_min: -1.57,
    angle_max: 1.57,
    angle_increment: 0.785,
    range_min: 0.1,
    range_max: 10.0
  }
}

{
  timestamp: 1701424800.523,
  topic: "/console/log",
  data: {
    level: "WARN",
    message: "Battery level low: 15%",
    module: "power_management"
  }
}

{
  timestamp: 1701424800.623,
  topic: "/joint_states",
  data: {
    name: ["joint1", "joint2", "joint3"],
    position: [0.1, 0.2, 0.3],
    velocity: [0.01, 0.02, 0.03],
    effort: [1.0, 2.0, 3.0]
  }
}
